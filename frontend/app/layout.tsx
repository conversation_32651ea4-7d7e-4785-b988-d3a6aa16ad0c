import type { Metadata } from 'next';
import './globals.css';

export const metadata: Metadata = {
  title: 'ChainOps - CI/CD Platform',
  description: 'Modern CI/CD platform with real-time monitoring and deployment automation',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full">
      <body className="h-full bg-gray-50 antialiased">
        {children}
      </body>
    </html>
  );
}
