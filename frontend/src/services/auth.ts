import { api } from './api';
import {
  User,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  UpdateProfileRequest,
  ChangePasswordRequest,
} from '@/types/auth';

export const authService = {
  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>('/auth/login', {
      login: email,
      password,
    });
    return response.data;
  },

  async register(data: RegisterRequest): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>('/auth/register', data);
    return response.data;
  },

  async getCurrentUser(): Promise<User> {
    const response = await api.get<{ data: { user: User } }>('/auth/me');
    return response.data.data.user;
  },

  async updateProfile(data: UpdateProfileRequest): Promise<User> {
    const response = await api.put<{ data: { user: User } }>('/auth/me', data);
    return response.data.data.user;
  },

  async changePassword(data: ChangePasswordRequest): Promise<void> {
    await api.put('/auth/change-password', data);
  },

  async refreshToken(): Promise<{ token: string }> {
    const response = await api.post<{ data: { token: string } }>('/auth/refresh');
    return response.data.data;
  },

  async logout(): Promise<void> {
    // Note: This is a client-side logout
    // Server-side logout would require token blacklisting
    return Promise.resolve();
  },
};
