import { api } from './api';
import {
  Pipeline,
  PipelineRun,
  PipelineListResponse,
  PipelineFilters,
  PaginationParams,
  PipelineGraph,
  Pipeline3DNode,
} from '@/types/pipelines';

export const pipelineService = {
  // Get all pipelines with filtering and pagination
  async getPipelines(
    filters: PipelineFilters = {},
    pagination: PaginationParams = { page: 1, limit: 10 }
  ): Promise<PipelineListResponse> {
    try {
      const params = new URLSearchParams();
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());
      
      if (filters.search) params.append('search', filters.search);
      if (filters.projectId) params.append('projectId', filters.projectId);
      if (filters.status?.length) {
        filters.status.forEach(status => params.append('status', status));
      }
      if (filters.userId) params.append('userId', filters.userId);
      if (filters.dateRange?.start) params.append('startDate', filters.dateRange.start);
      if (filters.dateRange?.end) params.append('endDate', filters.dateRange.end);

      const response = await api.get<PipelineListResponse>(`/api/pipelines?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch pipelines:', error);
      return {
        data: [],
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: 0,
          pages: 0,
        },
      };
    }
  },

  // Get pipeline by ID
  async getPipeline(id: string): Promise<Pipeline | null> {
    try {
      const response = await api.get<{ data: { pipeline: Pipeline } }>(`/api/pipelines/${id}`);
      return response.data.data.pipeline;
    } catch (error) {
      console.error('Failed to fetch pipeline:', error);
      return null;
    }
  },

  // Get pipeline runs
  async getPipelineRuns(
    pipelineId: string,
    pagination: PaginationParams = { page: 1, limit: 10 }
  ): Promise<{ runs: PipelineRun[]; total: number }> {
    try {
      const response = await api.get<{
        data: { runs: PipelineRun[]; pagination: { total: number } };
      }>(`/api/pipelines/${pipelineId}/runs?page=${pagination.page}&limit=${pagination.limit}`);
      
      return {
        runs: response.data.data.runs,
        total: response.data.data.pagination.total,
      };
    } catch (error) {
      console.error('Failed to fetch pipeline runs:', error);
      return { runs: [], total: 0 };
    }
  },

  // Get pipeline run by ID
  async getPipelineRun(pipelineId: string, runId: string): Promise<PipelineRun | null> {
    try {
      const response = await api.get<{ data: { run: PipelineRun } }>(
        `/api/pipelines/${pipelineId}/runs/${runId}`
      );
      return response.data.data.run;
    } catch (error) {
      console.error('Failed to fetch pipeline run:', error);
      return null;
    }
  },

  // Trigger pipeline run
  async triggerPipeline(
    pipelineId: string,
    variables?: Record<string, any>
  ): Promise<PipelineRun | null> {
    try {
      const response = await api.post<{ data: { run: PipelineRun } }>(
        `/api/pipelines/${pipelineId}/trigger`,
        { variables }
      );
      return response.data.data.run;
    } catch (error) {
      console.error('Failed to trigger pipeline:', error);
      return null;
    }
  },

  // Cancel pipeline run
  async cancelPipelineRun(pipelineId: string, runId: string): Promise<boolean> {
    try {
      await api.post(`/api/pipelines/${pipelineId}/runs/${runId}/cancel`);
      return true;
    } catch (error) {
      console.error('Failed to cancel pipeline run:', error);
      return false;
    }
  },

  // Retry pipeline run
  async retryPipelineRun(pipelineId: string, runId: string): Promise<PipelineRun | null> {
    try {
      const response = await api.post<{ data: { run: PipelineRun } }>(
        `/api/pipelines/${pipelineId}/runs/${runId}/retry`
      );
      return response.data.data.run;
    } catch (error) {
      console.error('Failed to retry pipeline run:', error);
      return null;
    }
  },

  // Generate 2D graph data for pipeline visualization
  generatePipelineGraph(pipelineRun: PipelineRun): PipelineGraph {
    const nodes: PipelineGraph['nodes'] = [];
    const edges: PipelineGraph['edges'] = [];

    if (!pipelineRun.jobs) return { nodes, edges };

    // Create nodes for each job
    pipelineRun.jobs.forEach((job, index) => {
      const status = job.status.toLowerCase() as any;
      
      nodes.push({
        id: job.id,
        type: 'job',
        data: {
          label: job.name,
          status,
          job,
          duration: job.startedAt && job.finishedAt 
            ? new Date(job.finishedAt).getTime() - new Date(job.startedAt).getTime()
            : undefined,
          startedAt: job.startedAt,
          finishedAt: job.finishedAt,
        },
        position: { x: (index % 4) * 250, y: Math.floor(index / 4) * 150 },
        style: {
          background: getStatusColor(status),
          border: `2px solid ${getStatusBorderColor(status)}`,
          borderRadius: '8px',
          padding: '10px',
          minWidth: '200px',
        },
      });

      // Create edges based on job dependencies
      if (job.config?.needs) {
        job.config.needs.forEach((dependency: string) => {
          const dependencyJob = pipelineRun.jobs?.find(j => j.name === dependency);
          if (dependencyJob) {
            edges.push({
              id: `${dependencyJob.id}-${job.id}`,
              source: dependencyJob.id,
              target: job.id,
              type: 'smoothstep',
              animated: job.status === 'RUNNING',
              style: { stroke: '#6b7280', strokeWidth: 2 },
            });
          }
        });
      }
    });

    return { nodes, edges };
  },

  // Generate 3D graph data for pipeline visualization
  generate3DPipelineGraph(pipelineRun: PipelineRun): Pipeline3DNode[] {
    if (!pipelineRun.jobs) return [];

    return pipelineRun.jobs.map((job, index) => ({
      id: job.id,
      position: [
        (index % 4) * 3 - 4.5, // x: spread horizontally
        Math.floor(index / 4) * 2 - 2, // y: stack vertically
        0, // z: keep on same plane initially
      ] as [number, number, number],
      status: job.status,
      label: job.name,
      job,
      dependencies: job.config?.needs || [],
    }));
  },

  // Get pipeline statistics
  async getPipelineStats(pipelineId: string): Promise<{
    totalRuns: number;
    successRate: number;
    avgDuration: number;
    lastRun?: PipelineRun;
  }> {
    try {
      const response = await api.get<{
        data: {
          totalRuns: number;
          successRate: number;
          avgDuration: number;
          lastRun?: PipelineRun;
        };
      }>(`/api/pipelines/${pipelineId}/stats`);
      
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch pipeline stats:', error);
      return {
        totalRuns: 0,
        successRate: 0,
        avgDuration: 0,
      };
    }
  },
};

// Helper functions for status colors
function getStatusColor(status: string): string {
  switch (status) {
    case 'success':
      return '#10b981';
    case 'failed':
      return '#ef4444';
    case 'running':
      return '#3b82f6';
    case 'pending':
    case 'queued':
      return '#f59e0b';
    case 'cancelled':
      return '#6b7280';
    case 'skipped':
      return '#8b5cf6';
    default:
      return '#e5e7eb';
  }
}

function getStatusBorderColor(status: string): string {
  switch (status) {
    case 'success':
      return '#059669';
    case 'failed':
      return '#dc2626';
    case 'running':
      return '#2563eb';
    case 'pending':
    case 'queued':
      return '#d97706';
    case 'cancelled':
      return '#4b5563';
    case 'skipped':
      return '#7c3aed';
    default:
      return '#d1d5db';
  }
}
