import axios from 'axios';
import { api } from '../api';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('API Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock axios.create to return the mocked axios instance
    mockedAxios.create.mockReturnValue(mockedAxios);
  });

  describe('get', () => {
    it('should make a GET request', async () => {
      const mockData = { id: 1, name: 'Test' };
      mockedAxios.get.mockResolvedValue({ data: mockData });

      const result = await api.get('/test');

      expect(mockedAxios.get).toHaveBeenCalledWith('/test');
      expect(result).toEqual(mockData);
    });

    it('should handle GET request errors', async () => {
      const error = new Error('Network Error');
      mockedAxios.get.mockRejectedValue(error);

      await expect(api.get('/test')).rejects.toThrow('Network Error');
    });
  });

  describe('post', () => {
    it('should make a POST request with data', async () => {
      const mockData = { id: 1, name: 'Created' };
      const postData = { name: 'New Item' };
      mockedAxios.post.mockResolvedValue({ data: mockData });

      const result = await api.post('/test', postData);

      expect(mockedAxios.post).toHaveBeenCalledWith('/test', postData);
      expect(result).toEqual(mockData);
    });

    it('should handle POST request errors', async () => {
      const error = new Error('Validation Error');
      mockedAxios.post.mockRejectedValue(error);

      await expect(api.post('/test', {})).rejects.toThrow('Validation Error');
    });
  });

  describe('put', () => {
    it('should make a PUT request with data', async () => {
      const mockData = { id: 1, name: 'Updated' };
      const putData = { name: 'Updated Item' };
      mockedAxios.put.mockResolvedValue({ data: mockData });

      const result = await api.put('/test/1', putData);

      expect(mockedAxios.put).toHaveBeenCalledWith('/test/1', putData);
      expect(result).toEqual(mockData);
    });
  });

  describe('delete', () => {
    it('should make a DELETE request', async () => {
      mockedAxios.delete.mockResolvedValue({ data: null });

      const result = await api.delete('/test/1');

      expect(mockedAxios.delete).toHaveBeenCalledWith('/test/1');
      expect(result).toBeNull();
    });
  });

  describe('patch', () => {
    it('should make a PATCH request with data', async () => {
      const mockData = { id: 1, name: 'Patched' };
      const patchData = { name: 'Patched Item' };
      mockedAxios.patch.mockResolvedValue({ data: mockData });

      const result = await api.patch('/test/1', patchData);

      expect(mockedAxios.patch).toHaveBeenCalledWith('/test/1', patchData);
      expect(result).toEqual(mockData);
    });
  });

  describe('request interceptors', () => {
    it('should add authorization header when token exists', () => {
      // Mock localStorage
      const mockGetItem = jest.fn().mockReturnValue('test-token');
      Object.defineProperty(window, 'localStorage', {
        value: { getItem: mockGetItem },
        writable: true,
      });

      // The interceptor should be called when making a request
      expect(mockedAxios.create).toHaveBeenCalled();
    });
  });

  describe('response interceptors', () => {
    it('should handle 401 responses by redirecting to login', () => {
      const mockPush = jest.fn();
      // Mock next/router
      jest.doMock('next/router', () => ({
        useRouter: () => ({ push: mockPush }),
      }));

      // The response interceptor should handle 401 errors
      expect(mockedAxios.create).toHaveBeenCalled();
    });
  });
});
