import { RunnerService } from '../runner';
import { DockerService } from '../docker';
import { QueueService } from '../queue';
import { ApiService } from '../api';

// Mock dependencies
jest.mock('../docker');
jest.mock('../queue');
jest.mock('../api');

const mockDockerService = DockerService as jest.Mocked<typeof DockerService>;
const mockQueueService = QueueService as jest.Mocked<typeof QueueService>;
const mockApiService = ApiService as jest.Mocked<typeof ApiService>;

describe('RunnerService', () => {
  let runnerService: RunnerService;
  let mockApiServiceInstance: jest.Mocked<ApiService>;
  let mockDockerServiceInstance: jest.Mocked<DockerService>;
  let mockQueueServiceInstance: jest.Mocked<QueueService>;

  beforeEach(() => {
    mockApiServiceInstance = new mockApiService() as jest.Mocked<ApiService>;
    mockDockerServiceInstance = new mockDockerService() as jest.Mocked<DockerService>;
    mockQueueServiceInstance = new mockQueueService() as jest.Mocked<QueueService>;

    runnerService = new RunnerService(
      mockApiServiceInstance,
      mockDockerServiceInstance,
      mockQueueServiceInstance
    );
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize successfully', () => {
      expect(runnerService).toBeInstanceOf(RunnerService);
    });
  });

  describe('start', () => {
    it('should start the runner service', async () => {
      mockQueueServiceInstance.startProcessing = jest.fn().mockResolvedValue(undefined);

      await runnerService.start();

      expect(mockQueueServiceInstance.startProcessing).toHaveBeenCalled();
    });

    it('should handle start errors', async () => {
      mockQueueServiceInstance.startProcessing = jest.fn().mockRejectedValue(new Error('Queue connection failed'));

      await expect(runnerService.start()).rejects.toThrow('Queue connection failed');
    });
  });

  describe('stop', () => {
    it('should stop the runner service', async () => {
      mockQueueServiceInstance.stopProcessing = jest.fn().mockResolvedValue(undefined);
      mockDockerServiceInstance.cleanup = jest.fn().mockResolvedValue(undefined);

      await runnerService.stop();

      expect(mockQueueServiceInstance.stopProcessing).toHaveBeenCalled();
      expect(mockDockerServiceInstance.cleanup).toHaveBeenCalled();
    });
  });

  describe('getRunnerInfo', () => {
    it('should return runner information', async () => {
      mockQueueServiceInstance.getActiveJobs = jest.fn().mockResolvedValue([]);
      mockDockerServiceInstance.getSystemInfo = jest.fn().mockResolvedValue({
        version: '20.10.0',
        containers: 5,
        images: 10
      });
      mockApiServiceInstance.getRunnerId = jest.fn().mockReturnValue('runner-123');

      const info = await runnerService.getRunnerInfo();

      expect(info).toEqual({
        id: 'runner-123',
        version: expect.any(String),
        status: 'idle',
        activeJobs: 0,
        totalJobsProcessed: 0,
        systemInfo: {
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version,
          memory: expect.any(Object),
          uptime: expect.any(Number),
          docker: {
            version: '20.10.0',
            containers: 5,
            images: 10
          }
        }
      });
    });
  });


});
