import { RunnerService } from '../runner';
import { DockerService } from '../docker';
import { QueueService } from '../queue';
import { ApiService } from '../api';

// Mock dependencies
jest.mock('../docker');
jest.mock('../queue');
jest.mock('../api');

const mockDockerService = DockerService as jest.Mocked<typeof DockerService>;
const mockQueueService = QueueService as jest.Mocked<typeof QueueService>;
const mockApiService = ApiService as jest.Mocked<typeof ApiService>;

describe('RunnerService', () => {
  let runnerService: RunnerService;

  beforeEach(() => {
    runnerService = new RunnerService();
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize successfully', () => {
      expect(runnerService).toBeInstanceOf(RunnerService);
    });
  });

  describe('start', () => {
    it('should start the runner service', async () => {
      mockQueueService.prototype.connect = jest.fn().mockResolvedValue(undefined);
      mockDockerService.prototype.initialize = jest.fn().mockResolvedValue(undefined);

      await runnerService.start();

      expect(mockQueueService.prototype.connect).toHaveBeenCalled();
      expect(mockDockerService.prototype.initialize).toHaveBeenCalled();
    });

    it('should handle start errors', async () => {
      mockQueueService.prototype.connect = jest.fn().mockRejectedValue(new Error('Queue connection failed'));

      await expect(runnerService.start()).rejects.toThrow('Queue connection failed');
    });
  });

  describe('stop', () => {
    it('should stop the runner service', async () => {
      mockQueueService.prototype.disconnect = jest.fn().mockResolvedValue(undefined);
      mockDockerService.prototype.cleanup = jest.fn().mockResolvedValue(undefined);

      await runnerService.stop();

      expect(mockQueueService.prototype.disconnect).toHaveBeenCalled();
      expect(mockDockerService.prototype.cleanup).toHaveBeenCalled();
    });
  });

  describe('processJob', () => {
    it('should process a job successfully', async () => {
      const mockJob = {
        id: 'job-123',
        name: 'test-job',
        script: ['echo "Hello World"'],
        image: 'node:18',
      };

      mockDockerService.prototype.runContainer = jest.fn().mockResolvedValue({
        exitCode: 0,
        logs: 'Hello World\n',
      });

      mockApiService.prototype.updateJobStatus = jest.fn().mockResolvedValue(undefined);

      const result = await runnerService.processJob(mockJob);

      expect(result.success).toBe(true);
      expect(result.exitCode).toBe(0);
      expect(mockDockerService.prototype.runContainer).toHaveBeenCalledWith(mockJob);
      expect(mockApiService.prototype.updateJobStatus).toHaveBeenCalledWith(mockJob.id, 'success');
    });

    it('should handle job failure', async () => {
      const mockJob = {
        id: 'job-123',
        name: 'test-job',
        script: ['exit 1'],
        image: 'node:18',
      };

      mockDockerService.prototype.runContainer = jest.fn().mockResolvedValue({
        exitCode: 1,
        logs: 'Command failed\n',
      });

      mockApiService.prototype.updateJobStatus = jest.fn().mockResolvedValue(undefined);

      const result = await runnerService.processJob(mockJob);

      expect(result.success).toBe(false);
      expect(result.exitCode).toBe(1);
      expect(mockApiService.prototype.updateJobStatus).toHaveBeenCalledWith(mockJob.id, 'failed');
    });

    it('should handle job processing errors', async () => {
      const mockJob = {
        id: 'job-123',
        name: 'test-job',
        script: ['echo "Hello World"'],
        image: 'node:18',
      };

      mockDockerService.prototype.runContainer = jest.fn().mockRejectedValue(new Error('Docker error'));
      mockApiService.prototype.updateJobStatus = jest.fn().mockResolvedValue(undefined);

      const result = await runnerService.processJob(mockJob);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Docker error');
      expect(mockApiService.prototype.updateJobStatus).toHaveBeenCalledWith(mockJob.id, 'failed');
    });
  });

  describe('healthCheck', () => {
    it('should return healthy status when all services are working', async () => {
      mockDockerService.prototype.healthCheck = jest.fn().mockResolvedValue(true);
      mockQueueService.prototype.healthCheck = jest.fn().mockResolvedValue(true);
      mockApiService.prototype.healthCheck = jest.fn().mockResolvedValue(true);

      const health = await runnerService.healthCheck();

      expect(health.healthy).toBe(true);
      expect(health.services.docker).toBe(true);
      expect(health.services.queue).toBe(true);
      expect(health.services.api).toBe(true);
    });

    it('should return unhealthy status when a service is down', async () => {
      mockDockerService.prototype.healthCheck = jest.fn().mockResolvedValue(false);
      mockQueueService.prototype.healthCheck = jest.fn().mockResolvedValue(true);
      mockApiService.prototype.healthCheck = jest.fn().mockResolvedValue(true);

      const health = await runnerService.healthCheck();

      expect(health.healthy).toBe(false);
      expect(health.services.docker).toBe(false);
    });
  });
});
