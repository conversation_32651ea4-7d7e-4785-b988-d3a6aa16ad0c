import { DockerService } from '../docker';
import Docker from 'dockerode';

// Mock dockerode
jest.mock('dockerode');

const mockDocker = Docker as jest.MockedClass<typeof Docker>;

describe('DockerService', () => {
  let dockerService: DockerService;
  let mockDockerInstance: jest.Mocked<Docker>;

  beforeEach(() => {
    mockDockerInstance = {
      ping: jest.fn(),
      createContainer: jest.fn(),
      listContainers: jest.fn(),
      getContainer: jest.fn(),
      listImages: jest.fn(),
      pull: jest.fn(),
    } as any;

    mockDocker.mockImplementation(() => mockDockerInstance);
    dockerService = new DockerService();
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize Docker service', async () => {
      mockDockerInstance.ping.mockResolvedValue(undefined);

      await dockerService.initialize();

      expect(mockDockerInstance.ping).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      mockDockerInstance.ping.mockRejectedValue(new Error('Docker daemon not running'));

      await expect(dockerService.initialize()).rejects.toThrow('Docker daemon not running');
    });
  });

  describe('runContainer', () => {
    it('should run a container successfully', async () => {
      const mockJob = {
        id: 'job-123',
        name: 'test-job',
        script: ['echo "Hello World"'],
        image: 'node:18',
      };

      const mockContainer = {
        start: jest.fn().mockResolvedValue(undefined),
        wait: jest.fn().mockResolvedValue({ StatusCode: 0 }),
        logs: jest.fn().mockResolvedValue(Buffer.from('Hello World\n')),
        remove: jest.fn().mockResolvedValue(undefined),
      };

      mockDockerInstance.createContainer.mockResolvedValue(mockContainer as any);

      const result = await dockerService.runContainer(mockJob);

      expect(result.exitCode).toBe(0);
      expect(result.logs).toContain('Hello World');
      expect(mockDockerInstance.createContainer).toHaveBeenCalledWith({
        Image: mockJob.image,
        Cmd: mockJob.script,
        name: `chainops-job-${mockJob.id}`,
        AttachStdout: true,
        AttachStderr: true,
        WorkingDir: '/workspace',
        Env: expect.any(Array),
      });
    });

    it('should handle container execution failure', async () => {
      const mockJob = {
        id: 'job-123',
        name: 'test-job',
        script: ['exit 1'],
        image: 'node:18',
      };

      const mockContainer = {
        start: jest.fn().mockResolvedValue(undefined),
        wait: jest.fn().mockResolvedValue({ StatusCode: 1 }),
        logs: jest.fn().mockResolvedValue(Buffer.from('Command failed\n')),
        remove: jest.fn().mockResolvedValue(undefined),
      };

      mockDockerInstance.createContainer.mockResolvedValue(mockContainer as any);

      const result = await dockerService.runContainer(mockJob);

      expect(result.exitCode).toBe(1);
      expect(result.logs).toContain('Command failed');
    });

    it('should handle container creation errors', async () => {
      const mockJob = {
        id: 'job-123',
        name: 'test-job',
        script: ['echo "Hello World"'],
        image: 'invalid-image',
      };

      mockDockerInstance.createContainer.mockRejectedValue(new Error('Image not found'));

      await expect(dockerService.runContainer(mockJob)).rejects.toThrow('Image not found');
    });
  });

  describe('pullImage', () => {
    it('should pull an image successfully', async () => {
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'end') {
            callback();
          }
        }),
      };

      mockDockerInstance.pull.mockResolvedValue(mockStream as any);

      await dockerService.pullImage('node:18');

      expect(mockDockerInstance.pull).toHaveBeenCalledWith('node:18');
    });

    it('should handle image pull errors', async () => {
      mockDockerInstance.pull.mockRejectedValue(new Error('Image not found'));

      await expect(dockerService.pullImage('invalid-image')).rejects.toThrow('Image not found');
    });
  });

  describe('listContainers', () => {
    it('should list containers', async () => {
      const mockContainers = [
        { Id: 'container1', Names: ['/test1'], State: 'running' },
        { Id: 'container2', Names: ['/test2'], State: 'exited' },
      ];

      mockDockerInstance.listContainers.mockResolvedValue(mockContainers as any);

      const containers = await dockerService.listContainers();

      expect(containers).toEqual(mockContainers);
      expect(mockDockerInstance.listContainers).toHaveBeenCalledWith({ all: true });
    });
  });

  describe('cleanup', () => {
    it('should cleanup stopped containers', async () => {
      const mockContainers = [
        { Id: 'container1', Names: ['/chainops-job-1'], State: 'exited' },
        { Id: 'container2', Names: ['/chainops-job-2'], State: 'exited' },
      ];

      const mockContainer1 = { remove: jest.fn().mockResolvedValue(undefined) };
      const mockContainer2 = { remove: jest.fn().mockResolvedValue(undefined) };

      mockDockerInstance.listContainers.mockResolvedValue(mockContainers as any);
      mockDockerInstance.getContainer
        .mockReturnValueOnce(mockContainer1 as any)
        .mockReturnValueOnce(mockContainer2 as any);

      await dockerService.cleanup();

      expect(mockContainer1.remove).toHaveBeenCalled();
      expect(mockContainer2.remove).toHaveBeenCalled();
    });
  });

  describe('healthCheck', () => {
    it('should return true when Docker is healthy', async () => {
      mockDockerInstance.ping.mockResolvedValue(undefined);

      const isHealthy = await dockerService.healthCheck();

      expect(isHealthy).toBe(true);
    });

    it('should return false when Docker is unhealthy', async () => {
      mockDockerInstance.ping.mockRejectedValue(new Error('Docker daemon not responding'));

      const isHealthy = await dockerService.healthCheck();

      expect(isHealthy).toBe(false);
    });
  });
});
