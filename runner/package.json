{"name": "chainops-runner", "version": "1.0.0", "description": "ChainOps Job Runner Agent", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"axios": "^1.6.2", "dockerode": "^4.0.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "uuid": "^9.0.1", "tar-fs": "^3.0.4", "node-stream-zip": "^1.15.0", "ws": "^8.14.2", "ioredis": "^5.3.2", "bull": "^4.12.2"}, "devDependencies": {"@types/node": "^20.10.4", "@types/dockerode": "^3.3.23", "@types/uuid": "^9.0.7", "@types/tar-fs": "^2.0.4", "@types/ws": "^8.5.10", "@types/jest": "^29.5.8", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1"}, "engines": {"node": ">=18.0.0"}}