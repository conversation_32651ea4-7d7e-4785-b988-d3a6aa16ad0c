import { DatabaseService } from '../database';
import { PrismaClient } from '@prisma/client';

// Create comprehensive mock for PrismaClient
const mockPrismaInstance = {
  $connect: jest.fn(),
  $disconnect: jest.fn(),
  $queryRaw: jest.fn(),
  $executeRaw: jest.fn(),
  $transaction: jest.fn(),
  $on: jest.fn(),
  $use: jest.fn(),
};

// Mock Prisma Client
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => mockPrismaInstance),
}));

const mockPrismaClient = PrismaClient as jest.MockedClass<typeof PrismaClient>;

describe('DatabaseService Comprehensive Tests', () => {
  let originalInstance: any;

  beforeEach(() => {
    // Reset singleton instance
    originalInstance = (DatabaseService as any).instance;
    (DatabaseService as any).instance = null;
    
    // Reset all mocks
    jest.clearAllMocks();
    
    // Setup default mock implementations
    mockPrismaInstance.$connect.mockResolvedValue(undefined);
    mockPrismaInstance.$disconnect.mockResolvedValue(undefined);
    mockPrismaInstance.$queryRaw.mockResolvedValue([{ '?column?': 1 }]);
  });

  afterEach(() => {
    // Restore original instance
    (DatabaseService as any).instance = originalInstance;
    jest.restoreAllMocks();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      
      expect(instance1).toBe(instance2);
      expect(instance1).toBeDefined();
    });

    it('should create new instance if none exists', () => {
      const instance = DatabaseService.getInstance();
      
      expect(instance).toBeDefined();
      expect(mockPrismaClient).toHaveBeenCalledTimes(1);
    });

    it('should reuse existing instance', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      
      expect(instance1).toBe(instance2);
      expect(mockPrismaClient).toHaveBeenCalledTimes(1);
    });
  });

  describe('initialize', () => {
    it('should initialize database service successfully', async () => {
      await DatabaseService.initialize();
      
      expect(mockPrismaInstance.$connect).toHaveBeenCalledTimes(1);
    });

    it('should handle initialization errors', async () => {
      const error = new Error('Database connection failed');
      mockPrismaInstance.$connect.mockRejectedValueOnce(error);

      await expect(DatabaseService.initialize()).rejects.toThrow('Database connection failed');
      expect(mockPrismaInstance.$connect).toHaveBeenCalledTimes(1);
    });

    it('should handle multiple initialization calls', async () => {
      await DatabaseService.initialize();
      await DatabaseService.initialize();
      
      expect(mockPrismaInstance.$connect).toHaveBeenCalledTimes(2);
    });

    it('should handle connection timeout', async () => {
      const timeoutError = new Error('Connection timeout');
      mockPrismaInstance.$connect.mockRejectedValueOnce(timeoutError);

      await expect(DatabaseService.initialize()).rejects.toThrow('Connection timeout');
    });
  });

  describe('close', () => {
    it('should close database connection successfully', async () => {
      await DatabaseService.close();
      
      expect(mockPrismaInstance.$disconnect).toHaveBeenCalledTimes(1);
    });

    it('should handle close errors', async () => {
      const error = new Error('Database disconnection failed');
      mockPrismaInstance.$disconnect.mockRejectedValueOnce(error);

      await expect(DatabaseService.close()).rejects.toThrow('Database disconnection failed');
      expect(mockPrismaInstance.$disconnect).toHaveBeenCalledTimes(1);
    });

    it('should handle multiple close calls', async () => {
      await DatabaseService.close();
      await DatabaseService.close();
      
      expect(mockPrismaInstance.$disconnect).toHaveBeenCalledTimes(2);
    });

    it('should handle disconnection timeout', async () => {
      const timeoutError = new Error('Disconnection timeout');
      mockPrismaInstance.$disconnect.mockRejectedValueOnce(timeoutError);

      await expect(DatabaseService.close()).rejects.toThrow('Disconnection timeout');
    });
  });

  describe('getClient', () => {
    it('should return prisma client', () => {
      const client = DatabaseService.getClient();
      
      expect(client).toBe(mockPrismaInstance);
      expect(client).toBeDefined();
    });

    it('should return same client instance', () => {
      const client1 = DatabaseService.getClient();
      const client2 = DatabaseService.getClient();
      
      expect(client1).toBe(client2);
    });

    it('should return client with all expected methods', () => {
      const client = DatabaseService.getClient();
      
      expect(typeof client.$connect).toBe('function');
      expect(typeof client.$disconnect).toBe('function');
      expect(typeof client.$queryRaw).toBe('function');
    });
  });

  describe('healthCheck', () => {
    it('should return true when database is healthy', async () => {
      mockPrismaInstance.$queryRaw.mockResolvedValue([{ '?column?': 1 }]);

      const isHealthy = await DatabaseService.healthCheck();

      expect(isHealthy).toBe(true);
      expect(mockPrismaInstance.$queryRaw).toHaveBeenCalledWith(expect.anything());
    });

    it('should return false when database is unhealthy', async () => {
      mockPrismaInstance.$queryRaw.mockRejectedValue(new Error('Connection failed'));

      const isHealthy = await DatabaseService.healthCheck();

      expect(isHealthy).toBe(false);
    });

    it('should handle timeout errors', async () => {
      mockPrismaInstance.$queryRaw.mockRejectedValue(new Error('Query timeout'));

      const isHealthy = await DatabaseService.healthCheck();

      expect(isHealthy).toBe(false);
    });

    it('should handle network errors', async () => {
      mockPrismaInstance.$queryRaw.mockRejectedValue(new Error('Network error'));

      const isHealthy = await DatabaseService.healthCheck();

      expect(isHealthy).toBe(false);
    });
  });

  describe('runMigrations', () => {
    it('should run migrations in development environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      await DatabaseService.runMigrations();

      process.env.NODE_ENV = originalEnv;
    });

    it('should run migrations in test environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'test';

      await DatabaseService.runMigrations();

      process.env.NODE_ENV = originalEnv;
    });

    it('should skip migrations in production environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      await DatabaseService.runMigrations();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle migration errors gracefully', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      // This test ensures the method doesn't throw in normal cases
      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle undefined NODE_ENV', async () => {
      const originalEnv = process.env.NODE_ENV;
      delete process.env.NODE_ENV;

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('error scenarios', () => {
    it('should handle concurrent initialization', async () => {
      mockPrismaInstance.$connect.mockResolvedValue(undefined);

      // Start multiple initializations concurrently
      const promises = [
        DatabaseService.initialize(),
        DatabaseService.initialize(),
        DatabaseService.initialize(),
      ];

      await Promise.all(promises);

      expect(mockPrismaInstance.$connect).toHaveBeenCalledTimes(3);
    });

    it('should handle concurrent close operations', async () => {
      mockPrismaInstance.$disconnect.mockResolvedValue(undefined);

      // Start multiple close operations concurrently
      const promises = [
        DatabaseService.close(),
        DatabaseService.close(),
        DatabaseService.close(),
      ];

      await Promise.all(promises);

      expect(mockPrismaInstance.$disconnect).toHaveBeenCalledTimes(3);
    });
  });

  describe('integration scenarios', () => {
    it('should handle full lifecycle', async () => {
      mockPrismaInstance.$connect.mockResolvedValue(undefined);
      mockPrismaInstance.$queryRaw.mockResolvedValue([{ '?column?': 1 }]);
      mockPrismaInstance.$disconnect.mockResolvedValue(undefined);

      // Initialize
      await DatabaseService.initialize();
      expect(mockPrismaInstance.$connect).toHaveBeenCalled();

      // Health check
      const isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(true);

      // Get client
      const client = DatabaseService.getClient();
      expect(client).toBeDefined();

      // Close
      await DatabaseService.close();
      expect(mockPrismaInstance.$disconnect).toHaveBeenCalled();
    });

    it('should handle initialize after close', async () => {
      mockPrismaInstance.$connect.mockResolvedValue(undefined);
      mockPrismaInstance.$disconnect.mockResolvedValue(undefined);

      await DatabaseService.close();
      await DatabaseService.initialize();

      expect(mockPrismaInstance.$disconnect).toHaveBeenCalled();
      expect(mockPrismaInstance.$connect).toHaveBeenCalled();
    });
  });
});
