import { DatabaseService } from '../database';
import { QueueService, JobData } from '../queue';
import { MinioService } from '../minio';

// Integration tests that actually call service methods
describe('Services Integration Tests', () => {
  describe('DatabaseService Real Methods', () => {
    it('should call getInstance and return instance', () => {
      const instance = DatabaseService.getInstance();
      expect(instance).toBeDefined();
      expect(instance.prisma).toBeDefined();
    });

    it('should call getClient and return client', () => {
      const client = DatabaseService.getClient();
      expect(client).toBeDefined();
    });

    it('should call initialize', async () => {
      await expect(DatabaseService.initialize()).resolves.not.toThrow();
    });

    it('should call healthCheck', async () => {
      const result = await DatabaseService.healthCheck();
      expect(typeof result).toBe('boolean');
    });

    it('should call runMigrations', async () => {
      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();
    });

    it('should call close', async () => {
      await expect(DatabaseService.close()).resolves.not.toThrow();
    });
  });

  describe('QueueService Real Methods', () => {
    beforeAll(async () => {
      await QueueService.initialize();
    });

    afterAll(async () => {
      await QueueService.close();
    });

    it('should call getInstance and return instance', () => {
      const instance = QueueService.getInstance();
      expect(instance).toBeDefined();
    });

    it('should call addPipelineJob', async () => {
      const jobData: JobData = {
        id: 'test-pipeline-1',
        type: 'pipeline',
        payload: { script: ['echo test'] },
        userId: 'test-user',
        pipelineId: 'test-pipeline'
      };

      try {
        const result = await QueueService.addPipelineJob(jobData);
        expect(result).toBeDefined();
      } catch (error) {
        // Redis might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call addJob', async () => {
      const jobData: JobData = {
        id: 'test-job-1',
        type: 'job',
        payload: { script: ['echo test'] },
        userId: 'test-user',
        pipelineId: 'test-pipeline'
      };

      try {
        const result = await QueueService.addJob(jobData);
        expect(result).toBeDefined();
      } catch (error) {
        // Redis might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call addNotification', async () => {
      const notificationData = {
        message: 'Test notification',
        userId: 'test-user'
      };

      try {
        const result = await QueueService.addNotification(notificationData);
        expect(result).toBeDefined();
      } catch (error) {
        // Redis might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call getPipelineQueueStats', async () => {
      try {
        const stats = await QueueService.getPipelineQueueStats();
        expect(stats).toBeDefined();
      } catch (error) {
        // Redis might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call getJobQueueStats', async () => {
      try {
        const stats = await QueueService.getJobQueueStats();
        expect(stats).toBeDefined();
      } catch (error) {
        // Redis might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call getPipelineQueue', () => {
      try {
        const queue = QueueService.getPipelineQueue();
        expect(queue).toBeDefined();
      } catch (error) {
        // Redis might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call getJobQueue', () => {
      try {
        const queue = QueueService.getJobQueue();
        expect(queue).toBeDefined();
      } catch (error) {
        // Redis might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call getNotificationQueue', () => {
      try {
        const queue = QueueService.getNotificationQueue();
        expect(queue).toBeDefined();
      } catch (error) {
        // Redis might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call healthCheck', async () => {
      const result = await QueueService.healthCheck();
      expect(typeof result).toBe('boolean');
    });
  });

  describe('MinioService Real Methods', () => {
    beforeAll(async () => {
      await MinioService.initialize();
    });

    it('should call getInstance and return instance', () => {
      const instance = MinioService.getInstance();
      expect(instance).toBeDefined();
    });

    it('should call uploadBuffer', async () => {
      const buffer = Buffer.from('test content');
      const objectName = 'test-file.txt';

      try {
        const result = await MinioService.uploadBuffer(objectName, buffer, buffer.length);
        expect(result).toBe(objectName);
      } catch (error) {
        // Minio might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call uploadFile', async () => {
      const filePath = '/tmp/test.txt';
      const objectName = 'test-upload.txt';

      try {
        const result = await MinioService.uploadFile(objectName, filePath);
        expect(result).toBe(objectName);
      } catch (error) {
        // File or Minio might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call downloadFile', async () => {
      const objectName = 'test-download.txt';

      try {
        const result = await MinioService.downloadFile(objectName);
        expect(result).toBeDefined();
      } catch (error) {
        // File or Minio might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call deleteFile', async () => {
      const objectName = 'test-delete.txt';

      try {
        await MinioService.deleteFile(objectName);
      } catch (error) {
        // File or Minio might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call listFiles', async () => {
      try {
        const result = await MinioService.listFiles();
        expect(Array.isArray(result)).toBe(true);
      } catch (error) {
        // Minio might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call getFileInfo', async () => {
      const objectName = 'test-info.txt';

      try {
        const result = await MinioService.getFileInfo(objectName);
        expect(result).toBeDefined();
      } catch (error) {
        // File or Minio might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call getFileUrl', async () => {
      const objectName = 'test-url.txt';

      try {
        const result = await MinioService.getFileUrl(objectName);
        expect(typeof result).toBe('string');
      } catch (error) {
        // File or Minio might not be available in test environment
        expect(error).toBeDefined();
      }
    });

    it('should call healthCheck', async () => {
      const result = await MinioService.healthCheck();
      expect(typeof result).toBe('boolean');
    });
  });
});
