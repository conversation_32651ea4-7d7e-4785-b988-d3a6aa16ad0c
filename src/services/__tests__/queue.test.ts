import { QueueService, JobData } from '../queue';
import Bull from 'bull';
import Redis from 'ioredis';

// Mock Bull
jest.mock('bull');
const mockBull = Bull as jest.MockedClass<typeof Bull>;

// Mock Redis
jest.mock('ioredis');
const mockRedis = Redis as jest.MockedClass<typeof Redis>;

describe('QueueService', () => {
  let mockPipelineQueue: jest.Mocked<Bull.Queue>;
  let mockJobQueue: jest.Mocked<Bull.Queue>;
  let mockNotificationQueue: jest.Mocked<Bull.Queue>;
  let mockRedisInstance: jest.Mocked<Redis>;

  beforeEach(() => {
    mockPipelineQueue = {
      add: jest.fn(),
      process: jest.fn(),
      on: jest.fn(),
      getJobs: jest.fn(),
      getJob: jest.fn(),
      getWaiting: jest.fn(),
      getActive: jest.fn(),
      getCompleted: jest.fn(),
      getFailed: jest.fn(),
      clean: jest.fn(),
      close: jest.fn(),
      pause: jest.fn(),
      resume: jest.fn(),
      removeJobs: jest.fn(),
    } as any;

    mockJobQueue = { ...mockPipelineQueue } as any;
    mockNotificationQueue = { ...mockPipelineQueue } as any;

    mockRedisInstance = {
      ping: jest.fn().mockResolvedValue('PONG'),
      quit: jest.fn().mockResolvedValue(undefined),
      on: jest.fn(),
    } as any;

    mockBull.mockImplementation((name: string) => {
      if (name === 'pipeline-queue') return mockPipelineQueue;
      if (name === 'job-queue') return mockJobQueue;
      if (name === 'notification-queue') return mockNotificationQueue;
      return mockJobQueue;
    });

    mockRedis.mockImplementation(() => mockRedisInstance);

    // Mock the getInstance method
    jest.spyOn(QueueService, 'getInstance' as any).mockReturnValue({
      redis: mockRedisInstance,
      pipelineQueue: mockPipelineQueue,
      jobQueue: mockJobQueue,
      notificationQueue: mockNotificationQueue,
    } as any);

    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('initialization', () => {
    it('should initialize queue service', async () => {
      await QueueService.initialize();
      expect(mockBull).toHaveBeenCalledTimes(3); // pipeline, job, notification queues
    });
  });

  describe('addPipelineJob', () => {
    it('should add pipeline job to queue successfully', async () => {
      const jobData: JobData = {
        id: 'job-1',
        type: 'pipeline',
        payload: { script: ['echo "hello"'] },
        userId: 'user-1',
        pipelineId: 'pipeline-1'
      };
      const mockJob = { id: 'bull-job-1', data: jobData };
      mockPipelineQueue.add.mockResolvedValue(mockJob as any);

      const result = await QueueService.addPipelineJob(jobData);

      expect(result).toEqual(mockJob);
      expect(mockPipelineQueue.add).toHaveBeenCalledWith('execute-pipeline', jobData, undefined);
    });
  });

  describe('addJob', () => {
    it('should add job to queue successfully', async () => {
      const jobData: JobData = {
        id: 'job-1',
        type: 'job',
        payload: { script: ['echo "hello"'] },
        userId: 'user-1',
        pipelineId: 'pipeline-1'
      };
      const mockJob = { id: 'bull-job-1', data: jobData };
      mockJobQueue.add.mockResolvedValue(mockJob as any);

      const result = await QueueService.addJob(jobData);

      expect(result).toEqual(mockJob);
      expect(mockJobQueue.add).toHaveBeenCalledWith('execute-job', jobData, undefined);
    });
  });

  describe('addNotification', () => {
    it('should add notification to queue successfully', async () => {
      const notificationData = { message: 'Test notification', userId: 'user-1' };
      const mockJob = { id: 'bull-job-1', data: notificationData };
      mockNotificationQueue.add.mockResolvedValue(mockJob as any);

      const result = await QueueService.addNotification(notificationData);

      expect(result).toEqual(mockJob);
      expect(mockNotificationQueue.add).toHaveBeenCalledWith('send-notification', notificationData, undefined);
    });
  });

  describe('getPipelineQueueStats', () => {
    it('should return pipeline queue statistics', async () => {
      const mockWaiting = [{ id: '1' }, { id: '2' }];
      const mockActive = [{ id: '3' }];
      const mockCompleted = [{ id: '4' }, { id: '5' }, { id: '6' }];
      const mockFailed = [{ id: '7' }];

      mockPipelineQueue.getWaiting.mockResolvedValue(mockWaiting as any);
      mockPipelineQueue.getActive.mockResolvedValue(mockActive as any);
      mockPipelineQueue.getCompleted.mockResolvedValue(mockCompleted as any);
      mockPipelineQueue.getFailed.mockResolvedValue(mockFailed as any);

      const stats = await QueueService.getPipelineQueueStats();

      expect(stats).toEqual({
        waiting: mockWaiting,
        active: mockActive,
        completed: mockCompleted,
        failed: mockFailed,
      });
    });
  });

  describe('getJobQueueStats', () => {
    it('should return job queue statistics', async () => {
      const mockWaiting = [{ id: '1' }, { id: '2' }];
      const mockActive = [{ id: '3' }];
      const mockCompleted = [{ id: '4' }, { id: '5' }, { id: '6' }];
      const mockFailed = [{ id: '7' }];

      mockJobQueue.getWaiting.mockResolvedValue(mockWaiting as any);
      mockJobQueue.getActive.mockResolvedValue(mockActive as any);
      mockJobQueue.getCompleted.mockResolvedValue(mockCompleted as any);
      mockJobQueue.getFailed.mockResolvedValue(mockFailed as any);

      const stats = await QueueService.getJobQueueStats();

      expect(stats).toEqual({
        waiting: mockWaiting,
        active: mockActive,
        completed: mockCompleted,
        failed: mockFailed,
      });
    });
  });

  describe('getPipelineQueue', () => {
    it('should return pipeline queue instance', () => {
      const queue = QueueService.getPipelineQueue();
      expect(queue).toBeDefined();
    });
  });

  describe('getJobQueue', () => {
    it('should return job queue instance', () => {
      const queue = QueueService.getJobQueue();
      expect(queue).toBeDefined();
    });
  });

  describe('getNotificationQueue', () => {
    it('should return notification queue instance', () => {
      const queue = QueueService.getNotificationQueue();
      expect(queue).toBeDefined();
    });
  });

  describe('healthCheck', () => {
    it('should return true when queue is healthy', async () => {
      mockRedisInstance.ping.mockResolvedValue('PONG');

      const isHealthy = await QueueService.healthCheck();

      expect(isHealthy).toBe(true);
      expect(mockRedisInstance.ping).toHaveBeenCalled();
    });

    it('should return false when queue is unhealthy', async () => {
      mockRedisInstance.ping.mockRejectedValue(new Error('Connection failed'));

      const isHealthy = await QueueService.healthCheck();

      expect(isHealthy).toBe(false);
    });
  });

  describe('close', () => {
    it('should close queue and redis connections', async () => {
      mockPipelineQueue.close.mockResolvedValue(undefined);
      mockJobQueue.close.mockResolvedValue(undefined);
      mockNotificationQueue.close.mockResolvedValue(undefined);
      mockRedisInstance.quit.mockResolvedValue(undefined);

      await QueueService.close();

      expect(mockPipelineQueue.close).toHaveBeenCalled();
      expect(mockJobQueue.close).toHaveBeenCalled();
      expect(mockNotificationQueue.close).toHaveBeenCalled();
      expect(mockRedisInstance.quit).toHaveBeenCalled();
    });

    it('should handle close errors gracefully', async () => {
      mockPipelineQueue.close.mockRejectedValue(new Error('Close error'));

      await expect(QueueService.close()).rejects.toThrow('Close error');
    });
  });
});
