import { QueueService } from '../queue';

// Mock Redis
const mockRedisClient = {
  connect: jest.fn(),
  disconnect: jest.fn(),
  ping: jest.fn(),
  lPush: jest.fn(),
  rPop: jest.fn(),
  lLen: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  isReady: true,
};

// Mock Redis constructor
jest.mock('redis', () => ({
  createClient: jest.fn(() => mockRedisClient),
}));

describe('QueueService', () => {
  beforeEach(() => {
    // Reset singleton instance
    (QueueService as any).instance = null;
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = QueueService.getInstance();
      const instance2 = QueueService.getInstance();
      expect(instance1).toBe(instance2);
      expect(instance1).toBeDefined();
    });

    it('should create new instance if none exists', () => {
      const instance = QueueService.getInstance();
      expect(instance).toBeDefined();
    });

    it('should reuse existing instance', () => {
      const instance1 = QueueService.getInstance();
      const instance2 = QueueService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('initialize', () => {
    it('should initialize queue service', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      await expect(QueueService.initialize()).resolves.not.toThrow();
      expect(mockRedisClient.connect).toHaveBeenCalled();
    });

    it('should handle multiple initialization calls', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      await QueueService.initialize();
      await QueueService.initialize();
      expect(mockRedisClient.connect).toHaveBeenCalledTimes(2);
    });

    it('should handle initialization errors', async () => {
      mockRedisClient.connect.mockRejectedValue(new Error('Connection failed'));
      await expect(QueueService.initialize()).rejects.toThrow('Connection failed');
    });

    it('should setup error handlers', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      await QueueService.initialize();
      expect(mockRedisClient.on).toHaveBeenCalledWith('error', expect.any(Function));
    });
  });

  describe('close', () => {
    it('should close queue connection', async () => {
      mockRedisClient.disconnect.mockResolvedValue(undefined);
      await expect(QueueService.close()).resolves.not.toThrow();
      expect(mockRedisClient.disconnect).toHaveBeenCalled();
    });

    it('should handle multiple close calls', async () => {
      mockRedisClient.disconnect.mockResolvedValue(undefined);
      await QueueService.close();
      await QueueService.close();
      expect(mockRedisClient.disconnect).toHaveBeenCalledTimes(2);
    });

    it('should handle close errors', async () => {
      mockRedisClient.disconnect.mockRejectedValue(new Error('Disconnect failed'));
      await expect(QueueService.close()).rejects.toThrow('Disconnect failed');
    });

    it('should remove error handlers', async () => {
      mockRedisClient.disconnect.mockResolvedValue(undefined);
      await QueueService.close();
      expect(mockRedisClient.off).toHaveBeenCalledWith('error', expect.any(Function));
    });
  });

  describe('healthCheck', () => {
    it('should perform health check successfully', async () => {
      mockRedisClient.ping.mockResolvedValue('PONG');
      const isHealthy = await QueueService.healthCheck();
      expect(isHealthy).toBe(true);
      expect(mockRedisClient.ping).toHaveBeenCalled();
    });

    it('should handle health check errors gracefully', async () => {
      mockRedisClient.ping.mockRejectedValue(new Error('Redis error'));
      const isHealthy = await QueueService.healthCheck();
      expect(isHealthy).toBe(false);
    });

    it('should return false for invalid ping response', async () => {
      mockRedisClient.ping.mockResolvedValue('INVALID');
      const isHealthy = await QueueService.healthCheck();
      expect(isHealthy).toBe(false);
    });

    it('should handle client not ready', async () => {
      mockRedisClient.isReady = false;
      const isHealthy = await QueueService.healthCheck();
      expect(isHealthy).toBe(false);
    });
  });

  describe('addJob', () => {
    it('should add job to queue', async () => {
      mockRedisClient.lPush.mockResolvedValue(1);
      const job = { id: 'job-1', type: 'build', data: { repo: 'test' } };
      
      await expect(QueueService.addJob('test-queue', job)).resolves.not.toThrow();
      expect(mockRedisClient.lPush).toHaveBeenCalledWith('test-queue', JSON.stringify(job));
    });

    it('should handle add job errors', async () => {
      mockRedisClient.lPush.mockRejectedValue(new Error('Redis error'));
      const job = { id: 'job-1', type: 'build', data: { repo: 'test' } };
      
      await expect(QueueService.addJob('test-queue', job)).rejects.toThrow('Redis error');
    });

    it('should handle complex job data', async () => {
      mockRedisClient.lPush.mockResolvedValue(1);
      const complexJob = {
        id: 'complex-job',
        type: 'deploy',
        data: {
          repo: 'test-repo',
          branch: 'main',
          environment: 'production',
          config: {
            replicas: 3,
            resources: { cpu: '500m', memory: '512Mi' }
          }
        }
      };
      
      await QueueService.addJob('deploy-queue', complexJob);
      expect(mockRedisClient.lPush).toHaveBeenCalledWith('deploy-queue', JSON.stringify(complexJob));
    });
  });

  describe('getJob', () => {
    it('should get job from queue', async () => {
      const job = { id: 'job-1', type: 'build', data: { repo: 'test' } };
      mockRedisClient.rPop.mockResolvedValue(JSON.stringify(job));
      
      const result = await QueueService.getJob('test-queue');
      expect(result).toEqual(job);
      expect(mockRedisClient.rPop).toHaveBeenCalledWith('test-queue');
    });

    it('should return null when queue is empty', async () => {
      mockRedisClient.rPop.mockResolvedValue(null);
      
      const result = await QueueService.getJob('empty-queue');
      expect(result).toBeNull();
    });

    it('should handle get job errors', async () => {
      mockRedisClient.rPop.mockRejectedValue(new Error('Redis error'));
      
      await expect(QueueService.getJob('test-queue')).rejects.toThrow('Redis error');
    });

    it('should handle invalid JSON', async () => {
      mockRedisClient.rPop.mockResolvedValue('invalid-json');
      
      await expect(QueueService.getJob('test-queue')).rejects.toThrow();
    });
  });

  describe('getQueueLength', () => {
    it('should get queue length', async () => {
      mockRedisClient.lLen.mockResolvedValue(5);
      
      const length = await QueueService.getQueueLength('test-queue');
      expect(length).toBe(5);
      expect(mockRedisClient.lLen).toHaveBeenCalledWith('test-queue');
    });

    it('should handle get length errors', async () => {
      mockRedisClient.lLen.mockRejectedValue(new Error('Redis error'));
      
      await expect(QueueService.getQueueLength('test-queue')).rejects.toThrow('Redis error');
    });

    it('should return 0 for non-existent queue', async () => {
      mockRedisClient.lLen.mockResolvedValue(0);
      
      const length = await QueueService.getQueueLength('non-existent');
      expect(length).toBe(0);
    });
  });

  describe('clearQueue', () => {
    it('should clear queue', async () => {
      mockRedisClient.del.mockResolvedValue(1);
      
      await expect(QueueService.clearQueue('test-queue')).resolves.not.toThrow();
      expect(mockRedisClient.del).toHaveBeenCalledWith('test-queue');
    });

    it('should handle clear queue errors', async () => {
      mockRedisClient.del.mockRejectedValue(new Error('Redis error'));
      
      await expect(QueueService.clearQueue('test-queue')).rejects.toThrow('Redis error');
    });

    it('should handle clearing non-existent queue', async () => {
      mockRedisClient.del.mockResolvedValue(0);
      
      await expect(QueueService.clearQueue('non-existent')).resolves.not.toThrow();
    });
  });

  describe('queueExists', () => {
    it('should check if queue exists', async () => {
      mockRedisClient.exists.mockResolvedValue(1);
      
      const exists = await QueueService.queueExists('test-queue');
      expect(exists).toBe(true);
      expect(mockRedisClient.exists).toHaveBeenCalledWith('test-queue');
    });

    it('should return false for non-existent queue', async () => {
      mockRedisClient.exists.mockResolvedValue(0);
      
      const exists = await QueueService.queueExists('non-existent');
      expect(exists).toBe(false);
    });

    it('should handle exists check errors', async () => {
      mockRedisClient.exists.mockRejectedValue(new Error('Redis error'));
      
      await expect(QueueService.queueExists('test-queue')).rejects.toThrow('Redis error');
    });
  });

  describe('error scenarios', () => {
    it('should handle concurrent operations', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.disconnect.mockResolvedValue(undefined);
      mockRedisClient.lPush.mockResolvedValue(1);

      const operations = [];
      for (let i = 0; i < 3; i++) {
        operations.push(QueueService.initialize());
        operations.push(QueueService.addJob(`queue-${i}`, { id: `job-${i}`, type: 'test' }));
        operations.push(QueueService.close());
      }

      await Promise.all(operations);
      expect(mockRedisClient.connect).toHaveBeenCalledTimes(3);
      expect(mockRedisClient.lPush).toHaveBeenCalledTimes(3);
      expect(mockRedisClient.disconnect).toHaveBeenCalledTimes(3);
    });

    it('should handle service lifecycle', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.ping.mockResolvedValue('PONG');
      mockRedisClient.lPush.mockResolvedValue(1);
      mockRedisClient.disconnect.mockResolvedValue(undefined);

      await QueueService.initialize();
      const isHealthy = await QueueService.healthCheck();
      await QueueService.addJob('test', { id: 'test', type: 'test' });
      await QueueService.close();

      expect(isHealthy).toBe(true);
    });

    it('should handle network timeouts', async () => {
      mockRedisClient.ping.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );
      
      const isHealthy = await QueueService.healthCheck();
      expect(isHealthy).toBe(false);
    });
  });

  describe('integration scenarios', () => {
    it('should handle full queue workflow', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.lPush.mockResolvedValue(1);
      mockRedisClient.rPop.mockResolvedValue(JSON.stringify({ id: 'job-1', type: 'test' }));
      mockRedisClient.lLen.mockResolvedValue(0);
      mockRedisClient.disconnect.mockResolvedValue(undefined);

      await QueueService.initialize();
      
      const job = { id: 'job-1', type: 'test', data: { repo: 'test' } };
      await QueueService.addJob('workflow-queue', job);
      
      const retrievedJob = await QueueService.getJob('workflow-queue');
      expect(retrievedJob).toBeDefined();
      
      const length = await QueueService.getQueueLength('workflow-queue');
      expect(length).toBe(0);
      
      await QueueService.close();
    });

    it('should handle multiple queues', async () => {
      mockRedisClient.lPush.mockResolvedValue(1);
      mockRedisClient.exists.mockResolvedValue(1);

      const queues = ['build-queue', 'test-queue', 'deploy-queue'];
      
      for (const queue of queues) {
        await QueueService.addJob(queue, { id: `job-${queue}`, type: queue.split('-')[0] });
        const exists = await QueueService.queueExists(queue);
        expect(exists).toBe(true);
      }

      expect(mockRedisClient.lPush).toHaveBeenCalledTimes(3);
      expect(mockRedisClient.exists).toHaveBeenCalledTimes(3);
    });
  });
});
