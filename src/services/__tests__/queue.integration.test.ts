import { QueueService, JobData } from '../queue';

// Don't mock Bull or <PERSON><PERSON> for integration tests
describe('QueueService Integration Tests', () => {
  beforeAll(async () => {
    // Initialize the service
    await QueueService.initialize();
  });

  afterAll(async () => {
    // Clean up
    await QueueService.close();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = QueueService.getInstance();
      const instance2 = QueueService.getInstance();
      expect(instance1).toBe(instance2);
      expect(instance1).toBeDefined();
    });
  });

  describe('queue operations', () => {
    it('should add pipeline job', async () => {
      const jobData: JobData = {
        id: 'test-pipeline-job-1',
        type: 'pipeline',
        payload: { script: ['echo "test"'] },
        userId: 'test-user',
        pipelineId: 'test-pipeline'
      };

      // Should not throw
      await expect(QueueService.addPipelineJob(jobData)).resolves.toBeDefined();
    });

    it('should add regular job', async () => {
      const jobData: JobData = {
        id: 'test-job-1',
        type: 'job',
        payload: { script: ['echo "test"'] },
        userId: 'test-user',
        pipelineId: 'test-pipeline'
      };

      // Should not throw
      await expect(QueueService.addJob(jobData)).resolves.toBeDefined();
    });

    it('should add notification', async () => {
      const notificationData = {
        message: 'Test notification',
        userId: 'test-user',
        type: 'info'
      };

      // Should not throw
      await expect(QueueService.addNotification(notificationData)).resolves.toBeDefined();
    });

    it('should add job with options', async () => {
      const jobData: JobData = {
        id: 'test-job-with-options',
        type: 'job',
        payload: { script: ['echo "test"'] },
        userId: 'test-user',
        pipelineId: 'test-pipeline'
      };

      const options = {
        delay: 1000,
        attempts: 3,
        backoff: 'exponential'
      };

      // Should not throw
      await expect(QueueService.addJob(jobData, options)).resolves.toBeDefined();
    });
  });

  describe('queue statistics', () => {
    it('should get pipeline queue stats', async () => {
      const stats = await QueueService.getPipelineQueueStats();
      expect(stats).toBeDefined();
      expect(stats).toHaveProperty('waiting');
      expect(stats).toHaveProperty('active');
      expect(stats).toHaveProperty('completed');
      expect(stats).toHaveProperty('failed');
      expect(Array.isArray(stats.waiting)).toBe(true);
      expect(Array.isArray(stats.active)).toBe(true);
      expect(Array.isArray(stats.completed)).toBe(true);
      expect(Array.isArray(stats.failed)).toBe(true);
    });

    it('should get job queue stats', async () => {
      const stats = await QueueService.getJobQueueStats();
      expect(stats).toBeDefined();
      expect(stats).toHaveProperty('waiting');
      expect(stats).toHaveProperty('active');
      expect(stats).toHaveProperty('completed');
      expect(stats).toHaveProperty('failed');
      expect(Array.isArray(stats.waiting)).toBe(true);
      expect(Array.isArray(stats.active)).toBe(true);
      expect(Array.isArray(stats.completed)).toBe(true);
      expect(Array.isArray(stats.failed)).toBe(true);
    });
  });

  describe('queue access', () => {
    it('should get pipeline queue', () => {
      const queue = QueueService.getPipelineQueue();
      expect(queue).toBeDefined();
      expect(typeof queue.add).toBe('function');
      expect(typeof queue.process).toBe('function');
    });

    it('should get job queue', () => {
      const queue = QueueService.getJobQueue();
      expect(queue).toBeDefined();
      expect(typeof queue.add).toBe('function');
      expect(typeof queue.process).toBe('function');
    });

    it('should get notification queue', () => {
      const queue = QueueService.getNotificationQueue();
      expect(queue).toBeDefined();
      expect(typeof queue.add).toBe('function');
      expect(typeof queue.process).toBe('function');
    });
  });

  describe('health check', () => {
    it('should perform health check', async () => {
      const isHealthy = await QueueService.healthCheck();
      // Health check might fail in test environment, but method should not throw
      expect(typeof isHealthy).toBe('boolean');
    });
  });

  describe('service lifecycle', () => {
    it('should handle multiple initialize calls', async () => {
      await expect(QueueService.initialize()).resolves.not.toThrow();
      await expect(QueueService.initialize()).resolves.not.toThrow();
    });
  });

  describe('error handling', () => {
    it('should handle invalid job data gracefully', async () => {
      const invalidJobData = {
        id: '',
        type: 'invalid',
        payload: null,
        userId: '',
        pipelineId: ''
      } as any;

      // Should handle gracefully or throw appropriate error
      try {
        await QueueService.addJob(invalidJobData);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });
});
