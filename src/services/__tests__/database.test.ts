import { DatabaseService } from '../database';

describe('DatabaseService', () => {
  beforeEach(() => {
    // Reset singleton instance
    (DatabaseService as any).instance = null;
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      expect(instance1).toBe(instance2);
      expect(instance1).toBeDefined();
    });

    it('should create new instance if none exists', () => {
      const instance = DatabaseService.getInstance();
      expect(instance).toBeDefined();
    });

    it('should reuse existing instance', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('initialize', () => {
    it('should initialize database service', async () => {
      await expect(DatabaseService.initialize()).resolves.not.toThrow();
    });

    it('should handle multiple initialization calls', async () => {
      await expect(DatabaseService.initialize()).resolves.not.toThrow();
      await expect(DatabaseService.initialize()).resolves.not.toThrow();
    });
  });

  describe('close', () => {
    it('should close database connection', async () => {
      await expect(DatabaseService.close()).resolves.not.toThrow();
    });

    it('should handle multiple close calls', async () => {
      await expect(DatabaseService.close()).resolves.not.toThrow();
      await expect(DatabaseService.close()).resolves.not.toThrow();
    });
  });

  describe('getClient', () => {
    it('should return prisma client', () => {
      const client = DatabaseService.getClient();
      expect(client).toBeDefined();
      expect(typeof client.$connect).toBe('function');
      expect(typeof client.$disconnect).toBe('function');
    });

    it('should return same client instance', () => {
      const client1 = DatabaseService.getClient();
      const client2 = DatabaseService.getClient();
      expect(client1).toBe(client2);
    });
  });

  describe('healthCheck', () => {
    it('should perform health check', async () => {
      const isHealthy = await DatabaseService.healthCheck();
      expect(typeof isHealthy).toBe('boolean');
    });

    it('should handle health check errors gracefully', async () => {
      // Health check should not throw, just return false on error
      const isHealthy = await DatabaseService.healthCheck();
      expect(typeof isHealthy).toBe('boolean');
    });
  });

  describe('runMigrations', () => {
    it('should run migrations in development', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should skip migrations in production', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should run migrations in test environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'test';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle undefined NODE_ENV', async () => {
      const originalEnv = process.env.NODE_ENV;
      delete process.env.NODE_ENV;

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('error scenarios', () => {
    it('should handle concurrent operations', async () => {
      // Test concurrent initialization
      const initPromises = [
        DatabaseService.initialize(),
        DatabaseService.initialize(),
        DatabaseService.initialize(),
      ];
      await expect(Promise.all(initPromises)).resolves.not.toThrow();

      // Test concurrent close
      const closePromises = [
        DatabaseService.close(),
        DatabaseService.close(),
        DatabaseService.close(),
      ];
      await expect(Promise.all(closePromises)).resolves.not.toThrow();
    });

    it('should handle service lifecycle', async () => {
      // Full lifecycle test
      await DatabaseService.initialize();
      const isHealthy = await DatabaseService.healthCheck();
      const client = DatabaseService.getClient();
      await DatabaseService.close();

      expect(typeof isHealthy).toBe('boolean');
      expect(client).toBeDefined();
    });

    it('should handle initialize after close', async () => {
      await DatabaseService.close();
      await expect(DatabaseService.initialize()).resolves.not.toThrow();
    });

    it('should handle getClient when not initialized', () => {
      const client = DatabaseService.getClient();
      expect(client).toBeDefined();
    });
  });

  describe('integration scenarios', () => {
    it('should handle full service workflow', async () => {
      // Initialize service
      await DatabaseService.initialize();

      // Get client and verify it works
      const client = DatabaseService.getClient();
      expect(client).toBeDefined();

      // Check health
      const isHealthy = await DatabaseService.healthCheck();
      expect(typeof isHealthy).toBe('boolean');

      // Run migrations
      await DatabaseService.runMigrations();

      // Close service
      await DatabaseService.close();
    });

    it('should handle multiple service instances', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      const instance3 = DatabaseService.getInstance();

      expect(instance1).toBe(instance2);
      expect(instance2).toBe(instance3);
    });

    it('should handle rapid operations', async () => {
      const operations = [];
      
      // Add multiple operations
      for (let i = 0; i < 5; i++) {
        operations.push(DatabaseService.initialize());
        operations.push(DatabaseService.healthCheck());
        operations.push(DatabaseService.close());
      }

      await expect(Promise.all(operations)).resolves.not.toThrow();
    });
  });

  describe('edge cases', () => {
    it('should handle empty environment variables', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = '';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle invalid environment variables', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'invalid-env';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle service state consistency', async () => {
      // Test that service maintains consistent state
      const client1 = DatabaseService.getClient();
      await DatabaseService.initialize();
      const client2 = DatabaseService.getClient();
      await DatabaseService.close();
      const client3 = DatabaseService.getClient();

      expect(client1).toBe(client2);
      expect(client2).toBe(client3);
    });
  });
});
