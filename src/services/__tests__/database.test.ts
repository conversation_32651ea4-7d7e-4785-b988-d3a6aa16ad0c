import { DatabaseService } from '../database';
import { PrismaClient } from '@prisma/client';

// Mock Prisma Client
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $queryRaw: jest.fn(),
  })),
}));

const mockPrismaClient = PrismaClient as jest.MockedClass<typeof PrismaClient>;

describe('DatabaseService', () => {
  let mockPrismaInstance: jest.Mocked<PrismaClient>;

  beforeEach(() => {
    mockPrismaInstance = new mockPrismaClient() as jest.Mocked<PrismaClient>;
    // Mock the getInstance method to return our mock
    jest.spyOn(DatabaseService, 'getInstance' as any).mockReturnValue({
      prisma: mockPrismaInstance
    } as any);
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('initialization', () => {
    it('should initialize database service', async () => {
      mockPrismaInstance.$connect.mockResolvedValue(undefined);

      await DatabaseService.initialize();

      expect(mockPrismaInstance.$connect).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      const error = new Error('Database connection failed');
      mockPrismaInstance.$connect.mockRejectedValue(error);

      await expect(DatabaseService.initialize()).rejects.toThrow('Database connection failed');
    });
  });

  describe('close', () => {
    it('should close database connection successfully', async () => {
      mockPrismaInstance.$disconnect.mockResolvedValue(undefined);

      await DatabaseService.close();

      expect(mockPrismaInstance.$disconnect).toHaveBeenCalled();
    });

    it('should handle close errors', async () => {
      const error = new Error('Database disconnection failed');
      mockPrismaInstance.$disconnect.mockRejectedValue(error);

      await expect(DatabaseService.close()).rejects.toThrow('Database disconnection failed');
    });
  });

  describe('getClient', () => {
    it('should return prisma client', () => {
      const client = DatabaseService.getClient();
      expect(client).toBe(mockPrismaInstance);
    });
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should create new instance if none exists', () => {
      // Temporarily clear the instance
      const originalInstance = (DatabaseService as any).instance;
      (DatabaseService as any).instance = null;

      const instance = DatabaseService.getInstance();
      expect(instance).toBeDefined();
      expect(instance.prisma).toBeInstanceOf(PrismaClient);

      // Restore original instance
      (DatabaseService as any).instance = originalInstance;
    });
  });

  describe('healthCheck', () => {
    it('should return true when database is healthy', async () => {
      mockPrismaInstance.$queryRaw.mockResolvedValue([{ '?column?': 1 }]);

      const isHealthy = await DatabaseService.healthCheck();

      expect(isHealthy).toBe(true);
      expect(mockPrismaInstance.$queryRaw).toHaveBeenCalled();
    });

    it('should return false when database is unhealthy', async () => {
      mockPrismaInstance.$queryRaw.mockRejectedValue(new Error('Connection failed'));

      const isHealthy = await DatabaseService.healthCheck();

      expect(isHealthy).toBe(false);
    });
  });

  describe('runMigrations', () => {
    it('should run migrations in development', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      await DatabaseService.runMigrations();

      process.env.NODE_ENV = originalEnv;
    });

    it('should skip migrations in production', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      await DatabaseService.runMigrations();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle migration errors', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      // This test just ensures the method doesn't throw in normal cases
      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });
  });
});
