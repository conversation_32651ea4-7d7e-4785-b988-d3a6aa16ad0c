import { MinioService } from '../minio';

// Mock Minio Client
const mockMinioClient = {
  bucketExists: jest.fn(),
  makeBucket: jest.fn(),
  putObject: jest.fn(),
  getObject: jest.fn(),
  removeObject: jest.fn(),
  listObjects: jest.fn(),
  statObject: jest.fn(),
  presignedGetObject: jest.fn(),
  presignedPutObject: jest.fn(),
};

// Mock Minio constructor
jest.mock('minio', () => ({
  Client: jest.fn(() => mockMinioClient),
}));

describe('MinioService', () => {
  beforeEach(() => {
    // Reset singleton instance
    (MinioService as any).instance = null;
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = MinioService.getInstance();
      const instance2 = MinioService.getInstance();
      expect(instance1).toBe(instance2);
      expect(instance1).toBeDefined();
    });

    it('should create new instance if none exists', () => {
      const instance = MinioService.getInstance();
      expect(instance).toBeDefined();
    });

    it('should reuse existing instance', () => {
      const instance1 = MinioService.getInstance();
      const instance2 = MinioService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('initialize', () => {
    it('should initialize minio service', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      await expect(MinioService.initialize()).resolves.not.toThrow();
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
    });

    it('should create bucket if it does not exist', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      mockMinioClient.makeBucket.mockResolvedValue(undefined);
      
      await MinioService.initialize();
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
      expect(mockMinioClient.makeBucket).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      mockMinioClient.bucketExists.mockRejectedValue(new Error('Connection failed'));
      await expect(MinioService.initialize()).rejects.toThrow('Connection failed');
    });

    it('should handle bucket creation errors', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      mockMinioClient.makeBucket.mockRejectedValue(new Error('Bucket creation failed'));
      
      await expect(MinioService.initialize()).rejects.toThrow('Bucket creation failed');
    });
  });

  describe('healthCheck', () => {
    it('should perform health check successfully', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      const isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(true);
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
    });

    it('should handle health check errors gracefully', async () => {
      mockMinioClient.bucketExists.mockRejectedValue(new Error('Minio error'));
      const isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(false);
    });

    it('should return false when bucket does not exist', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      const isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(false);
    });
  });

  describe('uploadFile', () => {
    it('should upload file successfully', async () => {
      const mockEtag = 'mock-etag-123';
      mockMinioClient.putObject.mockResolvedValue({ etag: mockEtag });
      
      const buffer = Buffer.from('test file content');
      const result = await MinioService.uploadFile('test-file.txt', buffer);
      
      expect(result).toBe(mockEtag);
      expect(mockMinioClient.putObject).toHaveBeenCalledWith(
        expect.any(String), 
        'test-file.txt', 
        buffer
      );
    });

    it('should handle upload errors', async () => {
      mockMinioClient.putObject.mockRejectedValue(new Error('Upload failed'));
      
      const buffer = Buffer.from('test content');
      await expect(MinioService.uploadFile('test.txt', buffer)).rejects.toThrow('Upload failed');
    });

    it('should upload with metadata', async () => {
      const mockEtag = 'mock-etag-456';
      mockMinioClient.putObject.mockResolvedValue({ etag: mockEtag });
      
      const buffer = Buffer.from('test content');
      const metadata = { 'Content-Type': 'text/plain', 'x-custom': 'value' };
      
      const result = await MinioService.uploadFile('test.txt', buffer, metadata);
      expect(result).toBe(mockEtag);
      expect(mockMinioClient.putObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt',
        buffer,
        metadata
      );
    });
  });

  describe('downloadFile', () => {
    it('should download file successfully', async () => {
      const mockStream = { pipe: jest.fn(), on: jest.fn() };
      mockMinioClient.getObject.mockResolvedValue(mockStream);
      
      const stream = await MinioService.downloadFile('test-file.txt');
      expect(stream).toBe(mockStream);
      expect(mockMinioClient.getObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt'
      );
    });

    it('should handle download errors', async () => {
      mockMinioClient.getObject.mockRejectedValue(new Error('Download failed'));
      
      await expect(MinioService.downloadFile('test.txt')).rejects.toThrow('Download failed');
    });

    it('should handle non-existent files', async () => {
      mockMinioClient.getObject.mockRejectedValue(new Error('NoSuchKey'));
      
      await expect(MinioService.downloadFile('non-existent.txt')).rejects.toThrow('NoSuchKey');
    });
  });

  describe('deleteFile', () => {
    it('should delete file successfully', async () => {
      mockMinioClient.removeObject.mockResolvedValue(undefined);
      
      await expect(MinioService.deleteFile('test-file.txt')).resolves.not.toThrow();
      expect(mockMinioClient.removeObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt'
      );
    });

    it('should handle delete errors', async () => {
      mockMinioClient.removeObject.mockRejectedValue(new Error('Delete failed'));
      
      await expect(MinioService.deleteFile('test.txt')).rejects.toThrow('Delete failed');
    });

    it('should handle deleting non-existent files', async () => {
      mockMinioClient.removeObject.mockRejectedValue(new Error('NoSuchKey'));
      
      await expect(MinioService.deleteFile('non-existent.txt')).rejects.toThrow('NoSuchKey');
    });
  });

  describe('listFiles', () => {
    it('should list files successfully', async () => {
      const mockObjects = [
        { name: 'file1.txt', size: 100 },
        { name: 'file2.txt', size: 200 },
      ];
      
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            mockObjects.forEach(obj => callback(obj));
          } else if (event === 'end') {
            callback();
          }
        }),
      };
      
      mockMinioClient.listObjects.mockReturnValue(mockStream);
      
      const files = await MinioService.listFiles();
      expect(files).toEqual(mockObjects);
      expect(mockMinioClient.listObjects).toHaveBeenCalledWith(
        expect.any(String),
        undefined,
        true
      );
    });

    it('should list files with prefix', async () => {
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'end') callback();
        }),
      };
      
      mockMinioClient.listObjects.mockReturnValue(mockStream);
      
      await MinioService.listFiles('logs/');
      expect(mockMinioClient.listObjects).toHaveBeenCalledWith(
        expect.any(String),
        'logs/',
        true
      );
    });

    it('should handle list errors', async () => {
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'error') {
            callback(new Error('List failed'));
          }
        }),
      };
      
      mockMinioClient.listObjects.mockReturnValue(mockStream);
      
      await expect(MinioService.listFiles()).rejects.toThrow('List failed');
    });
  });

  describe('getFileInfo', () => {
    it('should get file info successfully', async () => {
      const mockStat = {
        size: 1024,
        lastModified: new Date(),
        etag: 'mock-etag',
        contentType: 'text/plain',
      };
      
      mockMinioClient.statObject.mockResolvedValue(mockStat);
      
      const info = await MinioService.getFileInfo('test-file.txt');
      expect(info).toBe(mockStat);
      expect(mockMinioClient.statObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt'
      );
    });

    it('should handle file info errors', async () => {
      mockMinioClient.statObject.mockRejectedValue(new Error('Stat failed'));
      
      await expect(MinioService.getFileInfo('test.txt')).rejects.toThrow('Stat failed');
    });
  });

  describe('getPresignedUrl', () => {
    it('should get presigned URL for download', async () => {
      const mockUrl = 'https://minio.example.com/bucket/file.txt?signature=abc123';
      mockMinioClient.presignedGetObject.mockResolvedValue(mockUrl);
      
      const url = await MinioService.getPresignedUrl('test-file.txt', 'GET');
      expect(url).toBe(mockUrl);
      expect(mockMinioClient.presignedGetObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt',
        3600
      );
    });

    it('should get presigned URL for upload', async () => {
      const mockUrl = 'https://minio.example.com/bucket/file.txt?signature=def456';
      mockMinioClient.presignedPutObject.mockResolvedValue(mockUrl);
      
      const url = await MinioService.getPresignedUrl('test-file.txt', 'PUT');
      expect(url).toBe(mockUrl);
      expect(mockMinioClient.presignedPutObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt',
        3600
      );
    });

    it('should handle custom expiry time', async () => {
      const mockUrl = 'https://minio.example.com/bucket/file.txt?signature=ghi789';
      mockMinioClient.presignedGetObject.mockResolvedValue(mockUrl);
      
      await MinioService.getPresignedUrl('test-file.txt', 'GET', 7200);
      expect(mockMinioClient.presignedGetObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt',
        7200
      );
    });

    it('should handle presigned URL errors', async () => {
      mockMinioClient.presignedGetObject.mockRejectedValue(new Error('Presign failed'));
      
      await expect(MinioService.getPresignedUrl('test.txt', 'GET')).rejects.toThrow('Presign failed');
    });
  });

  describe('error scenarios', () => {
    it('should handle concurrent operations', async () => {
      mockMinioClient.putObject.mockResolvedValue({ etag: 'etag' });
      mockMinioClient.getObject.mockResolvedValue({ pipe: jest.fn() });
      mockMinioClient.removeObject.mockResolvedValue(undefined);

      const operations = [];
      for (let i = 0; i < 3; i++) {
        operations.push(MinioService.uploadFile(`file-${i}.txt`, Buffer.from(`content-${i}`)));
        operations.push(MinioService.downloadFile(`file-${i}.txt`));
        operations.push(MinioService.deleteFile(`file-${i}.txt`));
      }

      await Promise.all(operations);
      expect(mockMinioClient.putObject).toHaveBeenCalledTimes(3);
      expect(mockMinioClient.getObject).toHaveBeenCalledTimes(3);
      expect(mockMinioClient.removeObject).toHaveBeenCalledTimes(3);
    });

    it('should handle service lifecycle', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      mockMinioClient.putObject.mockResolvedValue({ etag: 'etag' });

      await MinioService.initialize();
      const isHealthy = await MinioService.healthCheck();
      await MinioService.uploadFile('test.txt', Buffer.from('test'));

      expect(isHealthy).toBe(true);
    });

    it('should handle network timeouts', async () => {
      mockMinioClient.bucketExists.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );
      
      const isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(false);
    });
  });

  describe('integration scenarios', () => {
    it('should handle full file workflow', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      mockMinioClient.putObject.mockResolvedValue({ etag: 'etag-123' });
      mockMinioClient.getObject.mockResolvedValue({ pipe: jest.fn() });
      mockMinioClient.statObject.mockResolvedValue({ size: 1024 });
      mockMinioClient.removeObject.mockResolvedValue(undefined);

      await MinioService.initialize();

      const buffer = Buffer.from('test file content');
      const etag = await MinioService.uploadFile('workflow-test.txt', buffer);
      expect(etag).toBe('etag-123');

      const stream = await MinioService.downloadFile('workflow-test.txt');
      expect(stream).toBeDefined();

      const info = await MinioService.getFileInfo('workflow-test.txt');
      expect(info.size).toBe(1024);

      await MinioService.deleteFile('workflow-test.txt');
    });

    it('should handle multiple file operations', async () => {
      mockMinioClient.putObject.mockResolvedValue({ etag: 'etag' });

      const files = [
        { name: 'file1.txt', content: 'content1' },
        { name: 'file2.txt', content: 'content2' },
        { name: 'file3.txt', content: 'content3' },
      ];

      for (const file of files) {
        await MinioService.uploadFile(file.name, Buffer.from(file.content));
      }

      expect(mockMinioClient.putObject).toHaveBeenCalledTimes(3);
    });

    it('should handle bucket creation workflow', async () => {
      // First call: bucket doesn't exist
      mockMinioClient.bucketExists.mockResolvedValueOnce(false);
      mockMinioClient.makeBucket.mockResolvedValueOnce(undefined);

      // Second call: bucket exists
      mockMinioClient.bucketExists.mockResolvedValueOnce(true);

      await MinioService.initialize();
      expect(mockMinioClient.bucketExists).toHaveBeenCalledTimes(1);
      expect(mockMinioClient.makeBucket).toHaveBeenCalledTimes(1);

      // Second initialization should not create bucket
      await MinioService.initialize();
      expect(mockMinioClient.bucketExists).toHaveBeenCalledTimes(2);
      expect(mockMinioClient.makeBucket).toHaveBeenCalledTimes(1);
    });

    it('should handle file versioning and metadata', async () => {
      const mockEtag1 = 'etag-v1';
      const mockEtag2 = 'etag-v2';

      mockMinioClient.putObject
        .mockResolvedValueOnce({ etag: mockEtag1 })
        .mockResolvedValueOnce({ etag: mockEtag2 });

      // Upload file v1
      const buffer1 = Buffer.from('version 1 content');
      const metadata1 = { 'x-version': '1', 'Content-Type': 'text/plain' };
      const etag1 = await MinioService.uploadFile('versioned-file.txt', buffer1, metadata1);
      expect(etag1).toBe(mockEtag1);

      // Upload file v2 (overwrite)
      const buffer2 = Buffer.from('version 2 content');
      const metadata2 = { 'x-version': '2', 'Content-Type': 'text/plain' };
      const etag2 = await MinioService.uploadFile('versioned-file.txt', buffer2, metadata2);
      expect(etag2).toBe(mockEtag2);

      expect(mockMinioClient.putObject).toHaveBeenCalledTimes(2);
    });

    it('should handle large file operations', async () => {
      const largeBuffer = Buffer.alloc(10 * 1024 * 1024); // 10MB
      mockMinioClient.putObject.mockResolvedValue({ etag: 'large-file-etag' });

      const etag = await MinioService.uploadFile('large-file.bin', largeBuffer);
      expect(etag).toBe('large-file-etag');

      expect(mockMinioClient.putObject).toHaveBeenCalledWith(
        expect.any(String),
        'large-file.bin',
        largeBuffer
      );
    });

    it('should handle concurrent file operations', async () => {
      mockMinioClient.putObject.mockResolvedValue({ etag: 'concurrent-etag' });
      mockMinioClient.getObject.mockResolvedValue({ pipe: jest.fn() });
      mockMinioClient.removeObject.mockResolvedValue(undefined);

      const operations = [];

      // Concurrent uploads
      for (let i = 0; i < 5; i++) {
        operations.push(
          MinioService.uploadFile(`concurrent-${i}.txt`, Buffer.from(`content-${i}`))
        );
      }

      // Concurrent downloads
      for (let i = 0; i < 5; i++) {
        operations.push(MinioService.downloadFile(`concurrent-${i}.txt`));
      }

      // Concurrent deletions
      for (let i = 0; i < 5; i++) {
        operations.push(MinioService.deleteFile(`concurrent-${i}.txt`));
      }

      await Promise.all(operations);

      expect(mockMinioClient.putObject).toHaveBeenCalledTimes(5);
      expect(mockMinioClient.getObject).toHaveBeenCalledTimes(5);
      expect(mockMinioClient.removeObject).toHaveBeenCalledTimes(5);
    });

    it('should handle file listing with pagination', async () => {
      const mockFiles = [
        { name: 'logs/2023/01/app.log', size: 1024 },
        { name: 'logs/2023/02/app.log', size: 2048 },
        { name: 'artifacts/build-123.zip', size: 5120 },
        { name: 'artifacts/build-124.zip', size: 6144 },
      ];

      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            mockFiles.forEach(file => callback(file));
          } else if (event === 'end') {
            callback();
          }
        }),
      };

      mockMinioClient.listObjects.mockReturnValue(mockStream);

      // List all files
      const allFiles = await MinioService.listFiles();
      expect(allFiles).toEqual(mockFiles);

      // List files with prefix
      await MinioService.listFiles('logs/');
      expect(mockMinioClient.listObjects).toHaveBeenCalledWith(
        expect.any(String),
        'logs/',
        true
      );
    });

    it('should handle presigned URL generation for different scenarios', async () => {
      const baseUrl = 'https://minio.example.com/bucket';

      mockMinioClient.presignedGetObject
        .mockResolvedValueOnce(`${baseUrl}/download.txt?signature=get123`)
        .mockResolvedValueOnce(`${baseUrl}/download.txt?signature=get456&expires=7200`);

      mockMinioClient.presignedPutObject
        .mockResolvedValueOnce(`${baseUrl}/upload.txt?signature=put123`)
        .mockResolvedValueOnce(`${baseUrl}/upload.txt?signature=put456&expires=1800`);

      // Default expiry download URL
      const downloadUrl1 = await MinioService.getPresignedUrl('download.txt', 'GET');
      expect(downloadUrl1).toContain('signature=get123');

      // Custom expiry download URL
      const downloadUrl2 = await MinioService.getPresignedUrl('download.txt', 'GET', 7200);
      expect(downloadUrl2).toContain('signature=get456');

      // Default expiry upload URL
      const uploadUrl1 = await MinioService.getPresignedUrl('upload.txt', 'PUT');
      expect(uploadUrl1).toContain('signature=put123');

      // Custom expiry upload URL
      const uploadUrl2 = await MinioService.getPresignedUrl('upload.txt', 'PUT', 1800);
      expect(uploadUrl2).toContain('signature=put456');
    });

    it('should handle error recovery and retry scenarios', async () => {
      // Simulate temporary failure then success
      mockMinioClient.bucketExists
        .mockRejectedValueOnce(new Error('Temporary network error'))
        .mockResolvedValueOnce(true);

      // First health check should fail
      let isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(false);

      // Second health check should succeed
      isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(true);
    });

    it('should handle file operations with special characters', async () => {
      mockMinioClient.putObject.mockResolvedValue({ etag: 'special-etag' });
      mockMinioClient.getObject.mockResolvedValue({ pipe: jest.fn() });
      mockMinioClient.removeObject.mockResolvedValue(undefined);

      const specialFiles = [
        'file with spaces.txt',
        'file-with-dashes.txt',
        'file_with_underscores.txt',
        'file.with.dots.txt',
        'файл-с-unicode.txt',
        'file@with#special$chars%.txt',
      ];

      for (const filename of specialFiles) {
        await MinioService.uploadFile(filename, Buffer.from('test content'));
        await MinioService.downloadFile(filename);
        await MinioService.deleteFile(filename);
      }

      expect(mockMinioClient.putObject).toHaveBeenCalledTimes(specialFiles.length);
      expect(mockMinioClient.getObject).toHaveBeenCalledTimes(specialFiles.length);
      expect(mockMinioClient.removeObject).toHaveBeenCalledTimes(specialFiles.length);
    });

    it('should handle service initialization edge cases', async () => {
      // Test multiple initialization calls
      mockMinioClient.bucketExists.mockResolvedValue(true);

      await MinioService.initialize();
      await MinioService.initialize();
      await MinioService.initialize();

      expect(mockMinioClient.bucketExists).toHaveBeenCalledTimes(3);
    });

    it('should handle file metadata and content type detection', async () => {
      mockMinioClient.putObject.mockResolvedValue({ etag: 'metadata-etag' });
      mockMinioClient.statObject.mockResolvedValue({
        size: 2048,
        lastModified: new Date('2023-01-01'),
        etag: 'metadata-etag',
        contentType: 'application/json',
        metadata: {
          'x-custom-header': 'custom-value',
          'x-upload-date': '2023-01-01',
        }
      });

      // Upload with custom metadata
      const jsonData = Buffer.from(JSON.stringify({ test: 'data' }));
      const metadata = {
        'Content-Type': 'application/json',
        'x-custom-header': 'custom-value',
        'x-upload-date': '2023-01-01',
      };

      await MinioService.uploadFile('data.json', jsonData, metadata);
      const fileInfo = await MinioService.getFileInfo('data.json');

      expect(fileInfo.contentType).toBe('application/json');
      expect(fileInfo.size).toBe(2048);
      expect(fileInfo.metadata).toEqual({
        'x-custom-header': 'custom-value',
        'x-upload-date': '2023-01-01',
      });
    });
  });

  describe('performance and reliability tests', () => {
    it('should handle high-throughput file operations', async () => {
      mockMinioClient.putObject.mockResolvedValue({ etag: 'perf-etag' });

      const startTime = Date.now();
      const fileCount = 100;

      const uploadPromises = [];
      for (let i = 0; i < fileCount; i++) {
        uploadPromises.push(
          MinioService.uploadFile(`perf-file-${i}.txt`, Buffer.from(`content-${i}`))
        );
      }

      await Promise.all(uploadPromises);
      const endTime = Date.now();

      expect(mockMinioClient.putObject).toHaveBeenCalledTimes(fileCount);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle memory-efficient large file operations', async () => {
      const largeBuffer = Buffer.alloc(50 * 1024 * 1024); // 50MB
      mockMinioClient.putObject.mockResolvedValue({ etag: 'large-etag' });

      await MinioService.uploadFile('large-file.bin', largeBuffer);

      expect(mockMinioClient.putObject).toHaveBeenCalledWith(
        expect.any(String),
        'large-file.bin',
        largeBuffer
      );
    });

    it('should handle network interruption during operations', async () => {
      // Simulate network interruption
      mockMinioClient.putObject
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockResolvedValueOnce({ etag: 'retry-etag' });

      try {
        await MinioService.uploadFile('network-test.txt', Buffer.from('test'));
      } catch (error) {
        expect(error.message).toBe('Network timeout');
      }

      // Retry should succeed
      const etag = await MinioService.uploadFile('network-test.txt', Buffer.from('test'));
      expect(etag).toBe('retry-etag');
    });
  });
});
