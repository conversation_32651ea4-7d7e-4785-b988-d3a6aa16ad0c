import { MinioService } from '../minio';

// Mock Minio Client
const mockMinioClient = {
  bucketExists: jest.fn(),
  makeBucket: jest.fn(),
  putObject: jest.fn(),
  getObject: jest.fn(),
  removeObject: jest.fn(),
  listObjects: jest.fn(),
  statObject: jest.fn(),
  presignedGetObject: jest.fn(),
  presignedPutObject: jest.fn(),
};

// Mock Minio constructor
jest.mock('minio', () => ({
  Client: jest.fn(() => mockMinioClient),
}));

describe('MinioService', () => {
  beforeEach(() => {
    // Reset singleton instance
    (MinioService as any).instance = null;
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = MinioService.getInstance();
      const instance2 = MinioService.getInstance();
      expect(instance1).toBe(instance2);
      expect(instance1).toBeDefined();
    });

    it('should create new instance if none exists', () => {
      const instance = MinioService.getInstance();
      expect(instance).toBeDefined();
    });

    it('should reuse existing instance', () => {
      const instance1 = MinioService.getInstance();
      const instance2 = MinioService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('initialize', () => {
    it('should initialize minio service', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      await expect(MinioService.initialize()).resolves.not.toThrow();
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
    });

    it('should create bucket if it does not exist', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      mockMinioClient.makeBucket.mockResolvedValue(undefined);
      
      await MinioService.initialize();
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
      expect(mockMinioClient.makeBucket).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      mockMinioClient.bucketExists.mockRejectedValue(new Error('Connection failed'));
      await expect(MinioService.initialize()).rejects.toThrow('Connection failed');
    });

    it('should handle bucket creation errors', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      mockMinioClient.makeBucket.mockRejectedValue(new Error('Bucket creation failed'));
      
      await expect(MinioService.initialize()).rejects.toThrow('Bucket creation failed');
    });
  });

  describe('healthCheck', () => {
    it('should perform health check successfully', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      const isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(true);
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
    });

    it('should handle health check errors gracefully', async () => {
      mockMinioClient.bucketExists.mockRejectedValue(new Error('Minio error'));
      const isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(false);
    });

    it('should return false when bucket does not exist', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      const isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(false);
    });
  });

  describe('uploadFile', () => {
    it('should upload file successfully', async () => {
      const mockEtag = 'mock-etag-123';
      mockMinioClient.putObject.mockResolvedValue({ etag: mockEtag });
      
      const buffer = Buffer.from('test file content');
      const result = await MinioService.uploadFile('test-file.txt', buffer);
      
      expect(result).toBe(mockEtag);
      expect(mockMinioClient.putObject).toHaveBeenCalledWith(
        expect.any(String), 
        'test-file.txt', 
        buffer
      );
    });

    it('should handle upload errors', async () => {
      mockMinioClient.putObject.mockRejectedValue(new Error('Upload failed'));
      
      const buffer = Buffer.from('test content');
      await expect(MinioService.uploadFile('test.txt', buffer)).rejects.toThrow('Upload failed');
    });

    it('should upload with metadata', async () => {
      const mockEtag = 'mock-etag-456';
      mockMinioClient.putObject.mockResolvedValue({ etag: mockEtag });
      
      const buffer = Buffer.from('test content');
      const metadata = { 'Content-Type': 'text/plain', 'x-custom': 'value' };
      
      const result = await MinioService.uploadFile('test.txt', buffer, metadata);
      expect(result).toBe(mockEtag);
      expect(mockMinioClient.putObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt',
        buffer,
        metadata
      );
    });
  });

  describe('downloadFile', () => {
    it('should download file successfully', async () => {
      const mockStream = { pipe: jest.fn(), on: jest.fn() };
      mockMinioClient.getObject.mockResolvedValue(mockStream);
      
      const stream = await MinioService.downloadFile('test-file.txt');
      expect(stream).toBe(mockStream);
      expect(mockMinioClient.getObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt'
      );
    });

    it('should handle download errors', async () => {
      mockMinioClient.getObject.mockRejectedValue(new Error('Download failed'));
      
      await expect(MinioService.downloadFile('test.txt')).rejects.toThrow('Download failed');
    });

    it('should handle non-existent files', async () => {
      mockMinioClient.getObject.mockRejectedValue(new Error('NoSuchKey'));
      
      await expect(MinioService.downloadFile('non-existent.txt')).rejects.toThrow('NoSuchKey');
    });
  });

  describe('deleteFile', () => {
    it('should delete file successfully', async () => {
      mockMinioClient.removeObject.mockResolvedValue(undefined);
      
      await expect(MinioService.deleteFile('test-file.txt')).resolves.not.toThrow();
      expect(mockMinioClient.removeObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt'
      );
    });

    it('should handle delete errors', async () => {
      mockMinioClient.removeObject.mockRejectedValue(new Error('Delete failed'));
      
      await expect(MinioService.deleteFile('test.txt')).rejects.toThrow('Delete failed');
    });

    it('should handle deleting non-existent files', async () => {
      mockMinioClient.removeObject.mockRejectedValue(new Error('NoSuchKey'));
      
      await expect(MinioService.deleteFile('non-existent.txt')).rejects.toThrow('NoSuchKey');
    });
  });

  describe('listFiles', () => {
    it('should list files successfully', async () => {
      const mockObjects = [
        { name: 'file1.txt', size: 100 },
        { name: 'file2.txt', size: 200 },
      ];
      
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            mockObjects.forEach(obj => callback(obj));
          } else if (event === 'end') {
            callback();
          }
        }),
      };
      
      mockMinioClient.listObjects.mockReturnValue(mockStream);
      
      const files = await MinioService.listFiles();
      expect(files).toEqual(mockObjects);
      expect(mockMinioClient.listObjects).toHaveBeenCalledWith(
        expect.any(String),
        undefined,
        true
      );
    });

    it('should list files with prefix', async () => {
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'end') callback();
        }),
      };
      
      mockMinioClient.listObjects.mockReturnValue(mockStream);
      
      await MinioService.listFiles('logs/');
      expect(mockMinioClient.listObjects).toHaveBeenCalledWith(
        expect.any(String),
        'logs/',
        true
      );
    });

    it('should handle list errors', async () => {
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'error') {
            callback(new Error('List failed'));
          }
        }),
      };
      
      mockMinioClient.listObjects.mockReturnValue(mockStream);
      
      await expect(MinioService.listFiles()).rejects.toThrow('List failed');
    });
  });

  describe('getFileInfo', () => {
    it('should get file info successfully', async () => {
      const mockStat = {
        size: 1024,
        lastModified: new Date(),
        etag: 'mock-etag',
        contentType: 'text/plain',
      };
      
      mockMinioClient.statObject.mockResolvedValue(mockStat);
      
      const info = await MinioService.getFileInfo('test-file.txt');
      expect(info).toBe(mockStat);
      expect(mockMinioClient.statObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt'
      );
    });

    it('should handle file info errors', async () => {
      mockMinioClient.statObject.mockRejectedValue(new Error('Stat failed'));
      
      await expect(MinioService.getFileInfo('test.txt')).rejects.toThrow('Stat failed');
    });
  });

  describe('getPresignedUrl', () => {
    it('should get presigned URL for download', async () => {
      const mockUrl = 'https://minio.example.com/bucket/file.txt?signature=abc123';
      mockMinioClient.presignedGetObject.mockResolvedValue(mockUrl);
      
      const url = await MinioService.getPresignedUrl('test-file.txt', 'GET');
      expect(url).toBe(mockUrl);
      expect(mockMinioClient.presignedGetObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt',
        3600
      );
    });

    it('should get presigned URL for upload', async () => {
      const mockUrl = 'https://minio.example.com/bucket/file.txt?signature=def456';
      mockMinioClient.presignedPutObject.mockResolvedValue(mockUrl);
      
      const url = await MinioService.getPresignedUrl('test-file.txt', 'PUT');
      expect(url).toBe(mockUrl);
      expect(mockMinioClient.presignedPutObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt',
        3600
      );
    });

    it('should handle custom expiry time', async () => {
      const mockUrl = 'https://minio.example.com/bucket/file.txt?signature=ghi789';
      mockMinioClient.presignedGetObject.mockResolvedValue(mockUrl);
      
      await MinioService.getPresignedUrl('test-file.txt', 'GET', 7200);
      expect(mockMinioClient.presignedGetObject).toHaveBeenCalledWith(
        expect.any(String),
        'test-file.txt',
        7200
      );
    });

    it('should handle presigned URL errors', async () => {
      mockMinioClient.presignedGetObject.mockRejectedValue(new Error('Presign failed'));
      
      await expect(MinioService.getPresignedUrl('test.txt', 'GET')).rejects.toThrow('Presign failed');
    });
  });

  describe('error scenarios', () => {
    it('should handle concurrent operations', async () => {
      mockMinioClient.putObject.mockResolvedValue({ etag: 'etag' });
      mockMinioClient.getObject.mockResolvedValue({ pipe: jest.fn() });
      mockMinioClient.removeObject.mockResolvedValue(undefined);

      const operations = [];
      for (let i = 0; i < 3; i++) {
        operations.push(MinioService.uploadFile(`file-${i}.txt`, Buffer.from(`content-${i}`)));
        operations.push(MinioService.downloadFile(`file-${i}.txt`));
        operations.push(MinioService.deleteFile(`file-${i}.txt`));
      }

      await Promise.all(operations);
      expect(mockMinioClient.putObject).toHaveBeenCalledTimes(3);
      expect(mockMinioClient.getObject).toHaveBeenCalledTimes(3);
      expect(mockMinioClient.removeObject).toHaveBeenCalledTimes(3);
    });

    it('should handle service lifecycle', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      mockMinioClient.putObject.mockResolvedValue({ etag: 'etag' });

      await MinioService.initialize();
      const isHealthy = await MinioService.healthCheck();
      await MinioService.uploadFile('test.txt', Buffer.from('test'));

      expect(isHealthy).toBe(true);
    });

    it('should handle network timeouts', async () => {
      mockMinioClient.bucketExists.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );
      
      const isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(false);
    });
  });

  describe('integration scenarios', () => {
    it('should handle full file workflow', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      mockMinioClient.putObject.mockResolvedValue({ etag: 'etag-123' });
      mockMinioClient.getObject.mockResolvedValue({ pipe: jest.fn() });
      mockMinioClient.statObject.mockResolvedValue({ size: 1024 });
      mockMinioClient.removeObject.mockResolvedValue(undefined);

      await MinioService.initialize();
      
      const buffer = Buffer.from('test file content');
      const etag = await MinioService.uploadFile('workflow-test.txt', buffer);
      expect(etag).toBe('etag-123');
      
      const stream = await MinioService.downloadFile('workflow-test.txt');
      expect(stream).toBeDefined();
      
      const info = await MinioService.getFileInfo('workflow-test.txt');
      expect(info.size).toBe(1024);
      
      await MinioService.deleteFile('workflow-test.txt');
    });

    it('should handle multiple file operations', async () => {
      mockMinioClient.putObject.mockResolvedValue({ etag: 'etag' });
      
      const files = [
        { name: 'file1.txt', content: 'content1' },
        { name: 'file2.txt', content: 'content2' },
        { name: 'file3.txt', content: 'content3' },
      ];

      for (const file of files) {
        await MinioService.uploadFile(file.name, Buffer.from(file.content));
      }

      expect(mockMinioClient.putObject).toHaveBeenCalledTimes(3);
    });
  });
});
