import { MinioService } from '../minio';
import { Client as MinioClient } from 'minio';
import { Readable } from 'stream';

// Mock Minio Client
jest.mock('minio', () => ({
  Client: jest.fn().mockImplementation(() => ({
    bucketExists: jest.fn(),
    makeBucket: jest.fn(),
    listBuckets: jest.fn(),
    putObject: jest.fn(),
    fPutObject: jest.fn(),
    getObject: jest.fn(),
    removeObject: jest.fn(),
    listObjects: jest.fn(),
    statObject: jest.fn(),
    presignedGetObject: jest.fn(),
    presignedPutObject: jest.fn(),
    copyObject: jest.fn(),
    setBucketPolicy: jest.fn(),
  })),
}));

const mockMinioClient = MinioClient as jest.MockedClass<typeof MinioClient>;

describe('MinioService', () => {
  let mockMinioInstance: jest.Mocked<MinioClient>;

  beforeEach(() => {
    mockMinioInstance = new mockMinioClient() as jest.Mocked<MinioClient>;
    
    // Mock the getInstance method
    jest.spyOn(MinioService, 'getInstance' as any).mockReturnValue({
      client: mockMinioInstance,
      bucketName: 'test-bucket'
    } as any);
    
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('initialization', () => {
    it('should initialize minio service', async () => {
      mockMinioInstance.bucketExists.mockResolvedValue(false);
      mockMinioInstance.makeBucket.mockResolvedValue(undefined);
      mockMinioInstance.setBucketPolicy.mockResolvedValue(undefined);

      await MinioService.initialize();

      expect(mockMinioInstance.bucketExists).toHaveBeenCalled();
      expect(mockMinioInstance.makeBucket).toHaveBeenCalled();
      expect(mockMinioInstance.setBucketPolicy).toHaveBeenCalled();
    });

    it('should handle initialization when bucket exists', async () => {
      mockMinioInstance.bucketExists.mockResolvedValue(true);
      mockMinioInstance.setBucketPolicy.mockResolvedValue(undefined);

      await MinioService.initialize();

      expect(mockMinioInstance.bucketExists).toHaveBeenCalled();
      expect(mockMinioInstance.makeBucket).not.toHaveBeenCalled();
      expect(mockMinioInstance.setBucketPolicy).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      mockMinioInstance.bucketExists.mockRejectedValue(new Error('Connection failed'));

      await expect(MinioService.initialize()).rejects.toThrow('Connection failed');
    });
  });

  describe('uploadFile', () => {
    it('should upload file successfully', async () => {
      const filePath = '/path/to/file.txt';
      const objectName = 'test-file.txt';
      mockMinioInstance.fPutObject.mockResolvedValue({ etag: 'test-etag' } as any);

      const result = await MinioService.uploadFile(objectName, filePath);

      expect(result).toBe(objectName);
      expect(mockMinioInstance.fPutObject).toHaveBeenCalledWith(
        'test-bucket',
        objectName,
        filePath,
        undefined
      );
    });

    it('should upload file with metadata', async () => {
      const filePath = '/path/to/file.txt';
      const objectName = 'test-file.txt';
      const metadata = { 'Content-Type': 'text/plain' };
      mockMinioInstance.fPutObject.mockResolvedValue({ etag: 'test-etag' } as any);

      const result = await MinioService.uploadFile(objectName, filePath, metadata);

      expect(result).toBe(objectName);
      expect(mockMinioInstance.fPutObject).toHaveBeenCalledWith(
        'test-bucket',
        objectName,
        filePath,
        metadata
      );
    });

    it('should handle upload errors', async () => {
      const filePath = '/path/to/file.txt';
      const objectName = 'test-file.txt';
      mockMinioInstance.fPutObject.mockRejectedValue(new Error('Upload failed'));

      await expect(MinioService.uploadFile(objectName, filePath)).rejects.toThrow('Upload failed');
    });
  });

  describe('uploadBuffer', () => {
    it('should upload buffer successfully', async () => {
      const buffer = Buffer.from('test content');
      const objectName = 'test-file.txt';
      mockMinioInstance.putObject.mockResolvedValue({ etag: 'test-etag' } as any);

      const result = await MinioService.uploadBuffer(objectName, buffer, buffer.length);

      expect(result).toBe(objectName);
      expect(mockMinioInstance.putObject).toHaveBeenCalledWith(
        'test-bucket',
        objectName,
        buffer,
        buffer.length,
        undefined
      );
    });

    it('should handle buffer upload errors', async () => {
      const buffer = Buffer.from('test content');
      const objectName = 'test-file.txt';
      mockMinioInstance.putObject.mockRejectedValue(new Error('Upload failed'));

      await expect(
        MinioService.uploadBuffer(objectName, buffer, buffer.length)
      ).rejects.toThrow('Upload failed');
    });
  });

  describe('downloadFile', () => {
    it('should download file successfully', async () => {
      const mockStream = new Readable();
      mockStream.push('test content');
      mockStream.push(null);
      mockMinioInstance.getObject.mockResolvedValue(mockStream);

      const result = await MinioService.downloadFile('test-file.txt');

      expect(result).toBeInstanceOf(Readable);
      expect(mockMinioInstance.getObject).toHaveBeenCalledWith('test-bucket', 'test-file.txt');
    });

    it('should handle download errors', async () => {
      mockMinioInstance.getObject.mockRejectedValue(new Error('Download failed'));

      await expect(MinioService.downloadFile('test-file.txt')).rejects.toThrow('Download failed');
    });
  });

  describe('deleteFile', () => {
    it('should delete file successfully', async () => {
      mockMinioInstance.removeObject.mockResolvedValue(undefined);

      await MinioService.deleteFile('test-file.txt');

      expect(mockMinioInstance.removeObject).toHaveBeenCalledWith('test-bucket', 'test-file.txt');
    });

    it('should handle delete errors', async () => {
      mockMinioInstance.removeObject.mockRejectedValue(new Error('Delete failed'));

      await expect(MinioService.deleteFile('test-file.txt')).rejects.toThrow('Delete failed');
    });
  });

  describe('listFiles', () => {
    it('should list files successfully', async () => {
      const mockObjects = [
        { name: 'file1.txt', size: 100 },
        { name: 'file2.txt', size: 200 }
      ];
      
      const mockStream = new Readable({ objectMode: true });
      mockObjects.forEach(obj => mockStream.push(obj));
      mockStream.push(null);
      
      mockMinioInstance.listObjects.mockReturnValue(mockStream as any);

      const result = await MinioService.listFiles();

      expect(result).toEqual(mockObjects);
      expect(mockMinioInstance.listObjects).toHaveBeenCalledWith('test-bucket', '', true);
    });

    it('should list files with prefix', async () => {
      const mockObjects = [{ name: 'logs/file1.txt', size: 100 }];
      
      const mockStream = new Readable({ objectMode: true });
      mockObjects.forEach(obj => mockStream.push(obj));
      mockStream.push(null);
      
      mockMinioInstance.listObjects.mockReturnValue(mockStream as any);

      const result = await MinioService.listFiles('logs/');

      expect(result).toEqual(mockObjects);
      expect(mockMinioInstance.listObjects).toHaveBeenCalledWith('test-bucket', 'logs/', true);
    });
  });

  describe('getFileInfo', () => {
    it('should get file info successfully', async () => {
      const mockStat = {
        size: 1024,
        lastModified: new Date(),
        etag: 'test-etag'
      };
      mockMinioInstance.statObject.mockResolvedValue(mockStat as any);

      const result = await MinioService.getFileInfo('test-file.txt');

      expect(result).toEqual(mockStat);
      expect(mockMinioInstance.statObject).toHaveBeenCalledWith('test-bucket', 'test-file.txt');
    });

    it('should handle file info errors', async () => {
      mockMinioInstance.statObject.mockRejectedValue(new Error('File not found'));

      await expect(MinioService.getFileInfo('test-file.txt')).rejects.toThrow('File not found');
    });
  });

  describe('getFileUrl', () => {
    it('should generate presigned GET URL', async () => {
      const mockUrl = 'https://minio.example.com/test-bucket/test-file.txt?signature=abc123';
      mockMinioInstance.presignedGetObject.mockResolvedValue(mockUrl);

      const result = await MinioService.getFileUrl('test-file.txt');

      expect(result).toBe(mockUrl);
      expect(mockMinioInstance.presignedGetObject).toHaveBeenCalledWith(
        'test-bucket',
        'test-file.txt',
        7 * 24 * 60 * 60 // 7 days default
      );
    });

    it('should generate presigned URL with custom expiry', async () => {
      const mockUrl = 'https://minio.example.com/test-bucket/test-file.txt?signature=abc123';
      mockMinioInstance.presignedGetObject.mockResolvedValue(mockUrl);

      const result = await MinioService.getFileUrl('test-file.txt', 3600);

      expect(result).toBe(mockUrl);
      expect(mockMinioInstance.presignedGetObject).toHaveBeenCalledWith(
        'test-bucket',
        'test-file.txt',
        3600
      );
    });

    it('should handle URL generation errors', async () => {
      mockMinioInstance.presignedGetObject.mockRejectedValue(new Error('URL generation failed'));

      await expect(MinioService.getFileUrl('test-file.txt')).rejects.toThrow('URL generation failed');
    });
  });

  describe('healthCheck', () => {
    it('should return true when minio is healthy', async () => {
      mockMinioInstance.bucketExists.mockResolvedValue(true);

      const isHealthy = await MinioService.healthCheck();

      expect(isHealthy).toBe(true);
      expect(mockMinioInstance.bucketExists).toHaveBeenCalledWith('test-bucket');
    });

    it('should return false when minio is unhealthy', async () => {
      mockMinioInstance.bucketExists.mockRejectedValue(new Error('Connection failed'));

      const isHealthy = await MinioService.healthCheck();

      expect(isHealthy).toBe(false);
    });
  });
});
