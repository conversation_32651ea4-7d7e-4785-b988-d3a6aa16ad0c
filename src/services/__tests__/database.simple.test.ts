import { DatabaseService } from '../database';
import { PrismaClient } from '@prisma/client';

// Mock Prisma Client
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $queryRaw: jest.fn(),
  })),
}));

const mockPrismaClient = PrismaClient as jest.MockedClass<typeof PrismaClient>;

describe('DatabaseService', () => {
  let mockPrismaInstance: jest.Mocked<PrismaClient>;

  beforeEach(() => {
    mockPrismaInstance = new mockPrismaClient() as jest.Mocked<PrismaClient>;
    // Mock the getInstance method to return our mock
    jest.spyOn(DatabaseService, 'getInstance' as any).mockReturnValue({
      prisma: mockPrismaInstance
    } as any);
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('initialization', () => {
    it('should initialize database service', async () => {
      mockPrismaInstance.$connect.mockResolvedValue(undefined);

      await DatabaseService.initialize();

      expect(mockPrismaInstance.$connect).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      const error = new Error('Database connection failed');
      mockPrismaInstance.$connect.mockRejectedValue(error);

      await expect(DatabaseService.initialize()).rejects.toThrow('Database connection failed');
    });

    it('should handle multiple initialization calls', async () => {
      mockPrismaInstance.$connect.mockResolvedValue(undefined);

      await DatabaseService.initialize();
      await DatabaseService.initialize();

      expect(mockPrismaInstance.$connect).toHaveBeenCalledTimes(2);
    });

    it('should handle connection timeout', async () => {
      const timeoutError = new Error('Connection timeout');
      mockPrismaInstance.$connect.mockRejectedValue(timeoutError);

      await expect(DatabaseService.initialize()).rejects.toThrow('Connection timeout');
    });
  });

  describe('close', () => {
    it('should close database connection successfully', async () => {
      mockPrismaInstance.$disconnect.mockResolvedValue(undefined);

      await DatabaseService.close();

      expect(mockPrismaInstance.$disconnect).toHaveBeenCalled();
    });

    it('should handle close errors', async () => {
      const error = new Error('Database disconnection failed');
      mockPrismaInstance.$disconnect.mockRejectedValue(error);

      await expect(DatabaseService.close()).rejects.toThrow('Database disconnection failed');
    });

    it('should handle multiple close calls', async () => {
      mockPrismaInstance.$disconnect.mockResolvedValue(undefined);

      await DatabaseService.close();
      await DatabaseService.close();

      expect(mockPrismaInstance.$disconnect).toHaveBeenCalledTimes(2);
    });

    it('should handle close when not connected', async () => {
      const error = new Error('Not connected');
      mockPrismaInstance.$disconnect.mockRejectedValue(error);

      await expect(DatabaseService.close()).rejects.toThrow('Not connected');
    });
  });

  describe('getClient', () => {
    it('should return prisma client', () => {
      const client = DatabaseService.getClient();
      expect(client).toBe(mockPrismaInstance);
    });

    it('should return same client instance', () => {
      const client1 = DatabaseService.getClient();
      const client2 = DatabaseService.getClient();
      expect(client1).toBe(client2);
    });

    it('should return client with all methods', () => {
      const client = DatabaseService.getClient();
      expect(client.$connect).toBeDefined();
      expect(client.$disconnect).toBeDefined();
      expect(client.$queryRaw).toBeDefined();
    });
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should create new instance if none exists', () => {
      // Temporarily clear the instance
      const originalInstance = (DatabaseService as any).instance;
      (DatabaseService as any).instance = null;

      const instance = DatabaseService.getInstance();
      expect(instance).toBeDefined();

      // Restore original instance
      (DatabaseService as any).instance = originalInstance;
    });
  });

  describe('healthCheck', () => {
    it('should return true when database is healthy', async () => {
      mockPrismaInstance.$queryRaw.mockResolvedValue([{ '?column?': 1 }]);

      const isHealthy = await DatabaseService.healthCheck();

      expect(isHealthy).toBe(true);
      expect(mockPrismaInstance.$queryRaw).toHaveBeenCalled();
    });

    it('should return false when database is unhealthy', async () => {
      mockPrismaInstance.$queryRaw.mockRejectedValue(new Error('Connection failed'));

      const isHealthy = await DatabaseService.healthCheck();

      expect(isHealthy).toBe(false);
    });

    it('should handle timeout errors', async () => {
      mockPrismaInstance.$queryRaw.mockRejectedValue(new Error('Query timeout'));

      const isHealthy = await DatabaseService.healthCheck();

      expect(isHealthy).toBe(false);
    });

    it('should handle network errors', async () => {
      mockPrismaInstance.$queryRaw.mockRejectedValue(new Error('Network error'));

      const isHealthy = await DatabaseService.healthCheck();

      expect(isHealthy).toBe(false);
    });
  });

  describe('runMigrations', () => {
    it('should run migrations in development', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      await DatabaseService.runMigrations();

      process.env.NODE_ENV = originalEnv;
    });

    it('should skip migrations in production', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      await DatabaseService.runMigrations();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle migration errors', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      // This test just ensures the method doesn't throw in normal cases
      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle undefined NODE_ENV', async () => {
      const originalEnv = process.env.NODE_ENV;
      delete process.env.NODE_ENV;

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should run migrations in test environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'test';

      await DatabaseService.runMigrations();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('error scenarios', () => {
    it('should handle concurrent operations', async () => {
      mockPrismaInstance.$connect.mockResolvedValue(undefined);
      mockPrismaInstance.$disconnect.mockResolvedValue(undefined);

      // Test concurrent initialization
      const initPromises = [
        DatabaseService.initialize(),
        DatabaseService.initialize(),
      ];
      await Promise.all(initPromises);

      // Test concurrent close
      const closePromises = [
        DatabaseService.close(),
        DatabaseService.close(),
      ];
      await Promise.all(closePromises);

      expect(mockPrismaInstance.$connect).toHaveBeenCalledTimes(2);
      expect(mockPrismaInstance.$disconnect).toHaveBeenCalledTimes(2);
    });

    it('should handle service lifecycle', async () => {
      mockPrismaInstance.$connect.mockResolvedValue(undefined);
      mockPrismaInstance.$queryRaw.mockResolvedValue([{ '?column?': 1 }]);
      mockPrismaInstance.$disconnect.mockResolvedValue(undefined);

      // Full lifecycle test
      await DatabaseService.initialize();
      const isHealthy = await DatabaseService.healthCheck();
      const client = DatabaseService.getClient();
      await DatabaseService.close();

      expect(isHealthy).toBe(true);
      expect(client).toBeDefined();
      expect(mockPrismaInstance.$connect).toHaveBeenCalled();
      expect(mockPrismaInstance.$disconnect).toHaveBeenCalled();
    });
  });
});
