import { DatabaseService } from '../database';

// Don't mock PrismaClient for integration tests
describe('DatabaseService Integration Tests', () => {
  beforeAll(async () => {
    // Initialize the service
    await DatabaseService.initialize();
  });

  afterAll(async () => {
    // Clean up
    await DatabaseService.close();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      expect(instance1).toBe(instance2);
      expect(instance1).toBeDefined();
      expect(instance1.prisma).toBeDefined();
    });
  });

  describe('getClient', () => {
    it('should return prisma client', () => {
      const client = DatabaseService.getClient();
      expect(client).toBeDefined();
      expect(typeof client.$connect).toBe('function');
      expect(typeof client.$disconnect).toBe('function');
    });
  });

  describe('healthCheck', () => {
    it('should perform health check', async () => {
      const isHealthy = await DatabaseService.healthCheck();
      // Health check might fail in test environment, but method should not throw
      expect(typeof isHealthy).toBe('boolean');
    });
  });

  describe('runMigrations', () => {
    it('should handle migrations in test environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'test';

      // Should not throw
      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle migrations in development environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      // Should not throw
      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should skip migrations in production environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      // Should not throw and should skip migrations
      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('service lifecycle', () => {
    it('should handle multiple initialize calls', async () => {
      await expect(DatabaseService.initialize()).resolves.not.toThrow();
      await expect(DatabaseService.initialize()).resolves.not.toThrow();
    });

    it('should handle close and reinitialize', async () => {
      await expect(DatabaseService.close()).resolves.not.toThrow();
      await expect(DatabaseService.initialize()).resolves.not.toThrow();
    });
  });

  describe('error handling', () => {
    it('should handle getClient when not initialized', () => {
      // Temporarily clear instance
      const originalInstance = (DatabaseService as any).instance;
      (DatabaseService as any).instance = null;

      const client = DatabaseService.getClient();
      expect(client).toBeDefined();

      // Restore instance
      (DatabaseService as any).instance = originalInstance;
    });
  });
});
