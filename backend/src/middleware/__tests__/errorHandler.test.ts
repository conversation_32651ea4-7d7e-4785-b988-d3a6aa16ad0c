import { Request, Response, NextFunction } from 'express';
import { errorHandler, CustomError } from '../errorHandler';

describe('Error Handler Middleware', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: NextFunction;

  beforeEach(() => {
    req = {};
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };
    next = jest.fn();
    jest.clearAllMocks();
  });

  describe('CustomError handling', () => {
    it('should handle CustomError with status code and message', () => {
      const error = new CustomError('Test error', 400);

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Test error',
          statusCode: 400,
        },
      });
    });

    it('should handle CustomError with isOperational flag', () => {
      const error = new CustomError('Operational error', 422, true);

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(422);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Operational error',
          statusCode: 422,
        },
      });
    });
  });

  describe('Custom error types', () => {
    it('should handle custom error with specific status code', () => {
      const error = new CustomError('Validation failed', 400);

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Validation failed',
          statusCode: 400,
        },
      });
    });

    it('should handle custom error with 500 status code', () => {
      const error = new CustomError('Database error', 500);

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Database error',
          statusCode: 500,
        },
      });
    });

    it('should handle custom error with 401 status code', () => {
      const error = new CustomError('Authentication failed', 401);

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Authentication failed',
          statusCode: 401,
        },
      });
    });

    it('should handle custom error with 403 status code', () => {
      const error = new CustomError('Access denied', 403);

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Access denied',
          statusCode: 403,
        },
      });
    });

    it('should handle custom error with 404 status code', () => {
      const error = new CustomError('Not found', 404);

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Not found',
          statusCode: 404,
        },
      });
    });
  });

  describe('Generic Error handling', () => {
    it('should handle generic Error with 500 status', () => {
      const error = new Error('Something went wrong');

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Something went wrong',
          statusCode: 500,
        },
      });
    });

    it('should handle unknown error types', () => {
      const error = 'String error';

      errorHandler(error as any, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: undefined,
          statusCode: 500,
        },
      });
    });
  });

  describe('Development vs Production', () => {
    it('should include stack trace in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const error = new Error('Test error');

      errorHandler(error, req as Request, res as Response, next);

      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Test error',
          statusCode: 500,
          stack: error.stack,
        },
      });

      process.env.NODE_ENV = originalEnv;
    });

    it('should not include stack trace in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const error = new Error('Test error');

      errorHandler(error, req as Request, res as Response, next);

      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Test error',
          statusCode: 500,
        },
      });

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('CustomError class', () => {
    it('should create CustomError with correct properties', () => {
      const error = new CustomError('Test error', 400, false);

      expect(error.message).toBe('Test error');
      expect(error.statusCode).toBe(400);
      expect(error.isOperational).toBe(false);
      expect(error.name).toBe('Error'); // Error.name is 'Error' by default
    });

    it('should create CustomError with default isOperational', () => {
      const error = new CustomError('Test error', 500);

      expect(error.message).toBe('Test error');
      expect(error.statusCode).toBe(500);
      expect(error.isOperational).toBe(true); // Default is true
      expect(error.name).toBe('Error');
    });
  });

  describe('Specific error types', () => {
    it('should handle ValidationError', () => {
      const error = new Error('Validation failed');
      error.name = 'ValidationError';

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Validation Error',
          statusCode: 400,
        },
      });
    });

    it('should handle JsonWebTokenError', () => {
      const error = new Error('Invalid token');
      error.name = 'JsonWebTokenError';

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Invalid token',
          statusCode: 401,
        },
      });
    });

    it('should handle TokenExpiredError', () => {
      const error = new Error('Token expired');
      error.name = 'TokenExpiredError';

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Token expired',
          statusCode: 401,
        },
      });
    });

    it('should handle PrismaClientKnownRequestError', () => {
      const error = new Error('Database error');
      error.name = 'PrismaClientKnownRequestError';

      errorHandler(error, req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          message: 'Database operation failed',
          statusCode: 400,
        },
      });
    });
  });
});
