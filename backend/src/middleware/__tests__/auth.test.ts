import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { authMiddleware, requireRole, requireAdmin, optionalAuth, AuthenticatedRequest } from '../auth';
import { CustomError } from '../errorHandler';

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    user: {
      findUnique: jest.fn(),
    },
  })),
}));

const mockJwt = jwt as jest.Mocked<typeof jwt>;

describe('Auth Middleware', () => {
  let req: Partial<AuthenticatedRequest>;
  let res: Partial<Response>;
  let next: NextFunction;

  beforeEach(() => {
    req = {
      headers: {},
    };
    res = {};
    next = jest.fn();
    jest.clearAllMocks();
    process.env.JWT_SECRET = 'test-secret';
  });

  describe('authMiddleware', () => {
    it('should throw error when no authorization header is provided', async () => {
      await authMiddleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access token required',
          statusCode: 401,
        })
      );
    });

    it('should throw error when authorization header does not start with Bearer', async () => {
      req.headers!.authorization = 'Basic token123';

      await authMiddleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access token required',
          statusCode: 401,
        })
      );
    });

    it('should throw error when no token is provided', async () => {
      req.headers!.authorization = 'Bearer ';

      await authMiddleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access token required',
          statusCode: 401,
        })
      );
    });

    it('should throw error when token is invalid', async () => {
      req.headers!.authorization = 'Bearer invalid-token';
      mockJwt.verify.mockImplementation(() => {
        throw new jwt.JsonWebTokenError('Invalid token');
      });

      await authMiddleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid token',
          statusCode: 401,
        })
      );
    });

    it('should throw error when token is expired', async () => {
      req.headers!.authorization = 'Bearer expired-token';
      mockJwt.verify.mockImplementation(() => {
        throw new jwt.TokenExpiredError('Token expired', new Date());
      });

      await authMiddleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Token expired',
          statusCode: 401,
        })
      );
    });
  });

  describe('requireRole', () => {
    it('should throw error when user is not authenticated', () => {
      const middleware = requireRole(['ADMIN']);

      expect(() => {
        middleware(req as AuthenticatedRequest, res as Response, next);
      }).toThrow('Authentication required');
    });

    it('should throw error when user does not have required role', () => {
      req.user = { id: '1', email: '<EMAIL>', username: 'test', role: 'USER' };
      const middleware = requireRole(['ADMIN']);

      expect(() => {
        middleware(req as AuthenticatedRequest, res as Response, next);
      }).toThrow('Insufficient permissions');
    });

    it('should call next when user has required role', () => {
      req.user = { id: '1', email: '<EMAIL>', username: 'test', role: 'ADMIN' };
      const middleware = requireRole(['ADMIN']);

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should allow multiple roles', () => {
      req.user = { id: '1', email: '<EMAIL>', username: 'test', role: 'MODERATOR' };
      const middleware = requireRole(['ADMIN', 'MODERATOR']);

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });
  });

  describe('requireAdmin', () => {
    it('should require ADMIN role', () => {
      req.user = { id: '1', email: '<EMAIL>', username: 'test', role: 'USER' };

      expect(() => {
        requireAdmin(req as AuthenticatedRequest, res as Response, next);
      }).toThrow('Insufficient permissions');
    });

    it('should allow ADMIN role', () => {
      req.user = { id: '1', email: '<EMAIL>', username: 'test', role: 'ADMIN' };

      requireAdmin(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });
  });

  describe('optionalAuth', () => {
    it('should continue without error when no auth header is provided', async () => {
      await optionalAuth(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
      expect(req.user).toBeUndefined();
    });

    it('should continue without error when invalid token is provided', async () => {
      req.headers!.authorization = 'Bearer invalid-token';
      mockJwt.verify.mockImplementation(() => {
        throw new jwt.JsonWebTokenError('Invalid token');
      });

      await optionalAuth(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
      expect(req.user).toBeUndefined();
    });
  });
});
