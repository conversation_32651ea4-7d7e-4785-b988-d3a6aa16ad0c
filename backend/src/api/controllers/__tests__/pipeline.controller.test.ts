import { Request, Response } from 'express';
import { PipelineController } from '../pipeline.controller';

describe('PipelineController', () => {
  let controller: PipelineController;
  let req: Partial<Request>;
  let res: Partial<Response>;

  beforeEach(() => {
    controller = new PipelineController();
    req = {};
    res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };
  });

  describe('listPipelines', () => {
    it('should return a list of pipelines', async () => {
      await controller.listPipelines(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith([
        { id: 1, name: 'Test Pipeline', status: 'success' }
      ]);
    });
  });

  describe('getPipeline', () => {
    it('should return a specific pipeline', async () => {
      req.params = { id: '123' };

      await controller.getPipeline(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith({
        id: 123,
        name: 'Test Pipeline',
        status: 'success'
      });
    });
  });

  describe('createPipeline', () => {
    it('should create a new pipeline', async () => {
      req.body = {
        name: 'New Pipeline',
        description: 'Test pipeline'
      };

      await controller.createPipeline(req as Request, res as Response);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        id: 1,
        name: 'New Pipeline',
        description: 'Test pipeline',
        status: 'pending'
      });
    });
  });

  describe('updatePipeline', () => {
    it('should update an existing pipeline', async () => {
      req.params = { id: '123' };
      req.body = {
        name: 'Updated Pipeline',
        description: 'Updated description'
      };

      await controller.updatePipeline(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith({
        id: 123,
        name: 'Updated Pipeline',
        description: 'Updated description',
        status: 'success'
      });
    });
  });

  describe('deletePipeline', () => {
    it('should delete a pipeline', async () => {
      req.params = { id: '123' };

      await controller.deletePipeline(req as Request, res as Response);

      expect(res.status).toHaveBeenCalledWith(204);
    });
  });

  describe('runPipeline', () => {
    it('should trigger a pipeline run', async () => {
      req.params = { id: '123' };
      req.body = {
        branch: 'main',
        variables: { ENV: 'production' }
      };

      await controller.runPipeline(req as Request, res as Response);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        id: expect.any(Number),
        pipelineId: 123,
        status: 'pending',
        branch: 'main',
        variables: { ENV: 'production' }
      });
    });
  });

  describe('getPipelineRuns', () => {
    it('should return pipeline runs', async () => {
      req.params = { id: '123' };

      await controller.getPipelineRuns(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith([
        {
          id: 1,
          pipelineId: 123,
          status: 'success',
          branch: 'main',
          createdAt: expect.any(String)
        }
      ]);
    });
  });

  describe('getPipelineRun', () => {
    it('should return a specific pipeline run', async () => {
      req.params = { id: '123', runId: '456' };

      await controller.getPipelineRun(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith({
        id: 456,
        pipelineId: 123,
        status: 'success',
        branch: 'main',
        jobs: expect.any(Array)
      });
    });
  });
});
