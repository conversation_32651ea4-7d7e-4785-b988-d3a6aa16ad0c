import { Request, Response } from 'express';

export class JobController {
    public listJobs = async (req: Request, res: Response) => {
        // Mock data for testing
        res.json([
            { id: 1, name: 'Test Job', status: 'success' }
        ]);
    };

    public getJob = async (req: Request, res: Response) => {
        const { id } = req.params;
        res.json({ id: parseInt(id), name: 'Test Job', status: 'success' });
    };

    public createJob = async (req: Request, res: Response) => {
        const job = req.body;
        res.status(201).json({ id: 1, ...job, status: 'pending' });
    };

    public updateJob = async (req: Request, res: Response) => {
        const { id } = req.params;
        const updates = req.body;
        res.json({ id: parseInt(id), ...updates, status: 'success' });
    };

    public deleteJob = async (req: Request, res: Response) => {
        const { id } = req.params;
        res.status(204).send();
    };
} 