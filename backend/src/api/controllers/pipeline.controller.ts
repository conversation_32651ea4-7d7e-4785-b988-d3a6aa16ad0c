import { Request, Response } from 'express';

export class PipelineController {
    public listPipelines = async (req: Request, res: Response) => {
        // Mock data for testing
        res.json([
            { id: 1, name: 'Test Pipeline', status: 'success' }
        ]);
    };

    public getPipeline = async (req: Request, res: Response) => {
        const { id } = req.params;
        res.json({ id: parseInt(id), name: 'Test Pipeline', status: 'success' });
    };

    public createPipeline = async (req: Request, res: Response) => {
        const pipeline = req.body;
        res.status(201).json({ id: 1, ...pipeline, status: 'pending' });
    };

    public updatePipeline = async (req: Request, res: Response) => {
        const { id } = req.params;
        const updates = req.body;
        res.json({ id: parseInt(id), ...updates, status: 'success' });
    };

    public deletePipeline = async (req: Request, res: Response) => {
        const { id } = req.params;
        res.status(204).send();
    };
} 