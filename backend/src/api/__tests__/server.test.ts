import request from 'supertest';
import { APIServer } from '../server';
import express from 'express';

describe('API Server', () => {
    let app: express.Application;

    beforeAll(async () => {
        const server = new APIServer();
        app = express();
        app.use('/api', server.router);
    });

    describe('GET /api/pipelines', () => {
        it('should return a list of pipelines', async () => {
            const response = await request(app)
                .get('/api/pipelines')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body.length).toBeGreaterThan(0);
            expect(response.body[0]).toHaveProperty('id');
            expect(response.body[0]).toHaveProperty('name');
            expect(response.body[0]).toHaveProperty('status');
        });
    });

    describe('GET /api/jobs', () => {
        it('should return a list of jobs', async () => {
            const response = await request(app)
                .get('/api/jobs')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body.length).toBeGreaterThan(0);
            expect(response.body[0]).toHaveProperty('id');
            expect(response.body[0]).toHaveProperty('name');
            expect(response.body[0]).toHaveProperty('status');
        });
    });

    describe('GET /api/secrets/:project/:job', () => {
        it('should return secrets for a project and job', async () => {
            const response = await request(app)
                .get('/api/secrets/test-project/test-job')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('project', 'test-project');
            expect(response.body).toHaveProperty('job', 'test-job');
            expect(response.body).toHaveProperty('secrets');
            expect(response.body.secrets).toHaveProperty('API_KEY');
        });
    });
}); 