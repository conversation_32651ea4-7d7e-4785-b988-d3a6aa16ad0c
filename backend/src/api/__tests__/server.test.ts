import request from 'supertest';
import { APIServer } from '../server';
import express from 'express';

describe('API Server', () => {
    let app: express.Application;
    let server: APIServer;

    beforeAll(async () => {
        server = new APIServer();
        app = express();
        app.use('/api', server.router);
    });

    describe('GET /api/pipelines', () => {
        it('should return a list of pipelines', async () => {
            const response = await request(app)
                .get('/api/pipelines')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body.length).toBeGreaterThan(0);
            expect(response.body[0]).toHaveProperty('id');
            expect(response.body[0]).toHaveProperty('name');
            expect(response.body[0]).toHaveProperty('status');
        });
    });

    describe('GET /api/jobs', () => {
        it('should return a list of jobs', async () => {
            const response = await request(app)
                .get('/api/jobs')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body.length).toBeGreaterThan(0);
            expect(response.body[0]).toHaveProperty('id');
            expect(response.body[0]).toHaveProperty('name');
            expect(response.body[0]).toHaveProperty('status');
        });
    });

    describe('GET /api/secrets/:project/:job', () => {
        it('should return secrets for a project and job', async () => {
            const response = await request(app)
                .get('/api/secrets/test-project/test-job')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('project', 'test-project');
            expect(response.body).toHaveProperty('job', 'test-job');
            expect(response.body).toHaveProperty('secrets');
            expect(response.body.secrets).toHaveProperty('API_KEY');
        });
    });

    describe('Server initialization', () => {
        it('should create server instance', () => {
            expect(server).toBeDefined();
            expect(server.router).toBeDefined();
        });

        it('should have proper middleware setup', () => {
            expect(server.router).toBeDefined();
        });
    });

    describe('Error handling', () => {
        it('should handle 404 errors', async () => {
            await request(app)
                .get('/api/nonexistent')
                .expect(404);
        });
    });

    describe('CORS handling', () => {
        it('should handle CORS preflight requests', async () => {
            await request(app)
                .options('/api/pipelines')
                .expect(200);
        });
    });

    describe('Content-Type handling', () => {
        it('should handle JSON requests', async () => {
            await request(app)
                .post('/api/pipelines')
                .send({ name: 'test-pipeline' })
                .set('Content-Type', 'application/json')
                .expect(201);
        });
    });

    describe('Authentication', () => {
        it('should handle authenticated requests', async () => {
            await request(app)
                .get('/api/pipelines')
                .set('Authorization', 'Bearer test-token')
                .expect(200);
        });
    });

    describe('GET /api/pipelines/:id', () => {
        it('should return a specific pipeline', async () => {
            const response = await request(app)
                .get('/api/pipelines/pipeline-1')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id');
            expect(response.body).toHaveProperty('name');
            expect(response.body).toHaveProperty('status');
            expect(response.body.id).toBe('pipeline-1');
        });

        it('should handle different pipeline IDs', async () => {
            const response = await request(app)
                .get('/api/pipelines/custom-pipeline-123')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id');
            expect(response.body).toHaveProperty('name');
            expect(response.body).toHaveProperty('status');
            expect(response.body.id).toBe('custom-pipeline-123');
        });

        it('should handle special characters in pipeline ID', async () => {
            const response = await request(app)
                .get('/api/pipelines/pipeline-with-special-chars-@#$%')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id');
            expect(response.body.id).toBe('pipeline-with-special-chars-@#$%');
        });
    });

    describe('POST /api/pipelines', () => {
        it('should create a new pipeline', async () => {
            const pipelineData = {
                name: 'Test Pipeline',
                description: 'A test pipeline',
                steps: ['build', 'test', 'deploy']
            };

            const response = await request(app)
                .post('/api/pipelines')
                .send(pipelineData)
                .expect('Content-Type', /json/)
                .expect(201);

            expect(response.body).toHaveProperty('id');
            expect(response.body).toHaveProperty('name', 'Test Pipeline');
            expect(response.body).toHaveProperty('description', 'A test pipeline');
            expect(response.body).toHaveProperty('steps');
        });

        it('should handle empty pipeline data', async () => {
            const response = await request(app)
                .post('/api/pipelines')
                .send({})
                .expect('Content-Type', /json/)
                .expect(201);

            expect(response.body).toHaveProperty('id');
        });

        it('should handle complex pipeline data', async () => {
            const complexPipelineData = {
                name: 'Complex Pipeline',
                description: 'A complex pipeline with nested configuration',
                steps: [
                    {
                        name: 'build',
                        commands: ['npm install', 'npm run build'],
                        environment: { NODE_ENV: 'production' }
                    },
                    {
                        name: 'test',
                        commands: ['npm test'],
                        environment: { NODE_ENV: 'test' }
                    }
                ],
                triggers: ['push', 'pull_request'],
                schedule: '0 0 * * *'
            };

            const response = await request(app)
                .post('/api/pipelines')
                .send(complexPipelineData)
                .expect('Content-Type', /json/)
                .expect(201);

            expect(response.body).toHaveProperty('id');
            expect(response.body).toHaveProperty('name', 'Complex Pipeline');
            expect(response.body).toHaveProperty('steps');
            expect(response.body).toHaveProperty('triggers');
            expect(response.body).toHaveProperty('schedule');
        });
    });

    describe('GET /api/jobs/:id', () => {
        it('should return a specific job', async () => {
            const response = await request(app)
                .get('/api/jobs/job-1')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id');
            expect(response.body).toHaveProperty('name');
            expect(response.body).toHaveProperty('status');
            expect(response.body.id).toBe('job-1');
        });

        it('should handle different job IDs', async () => {
            const response = await request(app)
                .get('/api/jobs/custom-job-456')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id');
            expect(response.body).toHaveProperty('name');
            expect(response.body).toHaveProperty('status');
            expect(response.body.id).toBe('custom-job-456');
        });

        it('should handle UUID job IDs', async () => {
            const uuid = '550e8400-e29b-41d4-a716-************';
            const response = await request(app)
                .get(`/api/jobs/${uuid}`)
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id');
            expect(response.body.id).toBe(uuid);
        });
    });

    describe('GET /api/jobs/:id/logs', () => {
        it('should attempt to return job logs', async () => {
            // This will likely fail in test environment since log file doesn't exist
            // but we're testing that the route handler is called
            await request(app)
                .get('/api/jobs/job-1/logs')
                .expect((res) => {
                    // Should either return file or 404/500 error
                    expect([200, 404, 500]).toContain(res.status);
                });
        });

        it('should handle different job IDs for logs', async () => {
            await request(app)
                .get('/api/jobs/test-job-123/logs')
                .expect((res) => {
                    expect([200, 404, 500]).toContain(res.status);
                });
        });

        it('should handle special characters in job ID for logs', async () => {
            await request(app)
                .get('/api/jobs/job-with-special-chars/logs')
                .expect((res) => {
                    expect([200, 404, 500]).toContain(res.status);
                });
        });
    });

    describe('GET /api/jobs/:id/artifacts', () => {
        it('should return job artifacts', async () => {
            const response = await request(app)
                .get('/api/jobs/job-1/artifacts')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body).toContain('artifacts/job-1_output.txt');
        });

        it('should handle different job IDs for artifacts', async () => {
            const response = await request(app)
                .get('/api/jobs/custom-job-789/artifacts')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body).toContain('artifacts/custom-job-789_output.txt');
        });

        it('should return empty array for jobs without artifacts', async () => {
            const response = await request(app)
                .get('/api/jobs/empty-job/artifacts')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body).toContain('artifacts/empty-job_output.txt');
        });
    });

    describe('Server Lifecycle', () => {
        let testServer: APIServer;

        beforeEach(() => {
            testServer = new APIServer();
        });

        afterEach(async () => {
            try {
                await testServer.stop();
            } catch (error) {
                // Server might not be running
            }
        });

        it('should start server on specified port', async () => {
            const port = 8082; // Use different port to avoid conflicts

            await expect(testServer.start(port)).resolves.not.toThrow();

            // Verify server is running by making a request
            const response = await request(`http://localhost:${port}`)
                .get('/api/pipelines')
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
        });

        it('should start server on default port when no port specified', async () => {
            await expect(testServer.start()).resolves.not.toThrow();
        });

        it('should stop server gracefully', async () => {
            const port = 8083;
            await testServer.start(port);

            await expect(testServer.stop()).resolves.not.toThrow();
        });

        it('should handle stop when server is not running', async () => {
            await expect(testServer.stop()).resolves.not.toThrow();
        });

        it('should handle multiple start calls', async () => {
            const port = 8084;
            await testServer.start(port);

            // Second start should not throw but might not start another server
            await expect(testServer.start(port + 1)).resolves.not.toThrow();
        });

        it('should handle multiple stop calls', async () => {
            const port = 8085;
            await testServer.start(port);
            await testServer.stop();

            // Second stop should not throw
            await expect(testServer.stop()).resolves.not.toThrow();
        });

        it('should handle start-stop-start cycle', async () => {
            const port = 8086;

            await testServer.start(port);
            await testServer.stop();
            await expect(testServer.start(port)).resolves.not.toThrow();
        });
    });

    describe('Error Handling in Routes', () => {
        it('should handle errors in pipeline creation', async () => {
            // Test with invalid JSON
            await request(app)
                .post('/api/pipelines')
                .send('invalid json')
                .set('Content-Type', 'application/json')
                .expect((res) => {
                    expect([400, 500]).toContain(res.status);
                });
        });

        it('should handle large payloads', async () => {
            const largePipeline = {
                name: 'Large Pipeline',
                steps: new Array(1000).fill('step'),
                data: 'x'.repeat(10000)
            };

            await request(app)
                .post('/api/pipelines')
                .send(largePipeline)
                .expect((res) => {
                    expect([201, 413, 500]).toContain(res.status);
                });
        });

        it('should handle special characters in URLs', async () => {
            await request(app)
                .get('/api/pipelines/test%20pipeline%20with%20spaces')
                .expect(200);
        });

        it('should handle very long URLs', async () => {
            const longId = 'a'.repeat(1000);
            await request(app)
                .get(`/api/pipelines/${longId}`)
                .expect(200);
        });
    });

    describe('Middleware Integration', () => {
        it('should apply security headers', async () => {
            const response = await request(app)
                .get('/api/pipelines')
                .expect(200);

            // Check for security headers (these might be set by helmet)
            expect(response.headers).toBeDefined();
        });

        it('should handle CORS preflight for all routes', async () => {
            const routes = [
                '/api/pipelines',
                '/api/jobs',
                '/api/secrets/test/test'
            ];

            for (const route of routes) {
                await request(app)
                    .options(route)
                    .expect((res) => {
                        expect([200, 204]).toContain(res.status);
                    });
            }
        });

        it('should handle rate limiting', async () => {
            // Make multiple rapid requests
            const promises = [];
            for (let i = 0; i < 10; i++) {
                promises.push(request(app).get('/api/pipelines'));
            }

            const responses = await Promise.all(promises);
            responses.forEach(response => {
                expect([200, 429]).toContain(response.status);
            });
        });
    });

    describe('Route Parameter Validation', () => {
        it('should handle empty route parameters', async () => {
            await request(app)
                .get('/api/pipelines/')
                .expect((res) => {
                    expect([200, 404]).toContain(res.status);
                });
        });

        it('should handle null route parameters', async () => {
            await request(app)
                .get('/api/pipelines/null')
                .expect(200);
        });

        it('should handle undefined route parameters', async () => {
            await request(app)
                .get('/api/pipelines/undefined')
                .expect(200);
        });
    });
});