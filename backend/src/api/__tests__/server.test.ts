import request from 'supertest';
import { APIServer } from '../server';
import express from 'express';

describe('API Server', () => {
    let app: express.Application;
    let server: APIServer;

    beforeAll(async () => {
        server = new APIServer();
        app = express();
        app.use('/api', server.router);
    });

    describe('GET /api/pipelines', () => {
        it('should return a list of pipelines', async () => {
            const response = await request(app)
                .get('/api/pipelines')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body.length).toBeGreaterThan(0);
            expect(response.body[0]).toHaveProperty('id');
            expect(response.body[0]).toHaveProperty('name');
            expect(response.body[0]).toHaveProperty('status');
        });
    });

    describe('GET /api/jobs', () => {
        it('should return a list of jobs', async () => {
            const response = await request(app)
                .get('/api/jobs')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body.length).toBeGreaterThan(0);
            expect(response.body[0]).toHaveProperty('id');
            expect(response.body[0]).toHaveProperty('name');
            expect(response.body[0]).toHaveProperty('status');
        });
    });

    describe('GET /api/secrets/:project/:job', () => {
        it('should return secrets for a project and job', async () => {
            const response = await request(app)
                .get('/api/secrets/test-project/test-job')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('project', 'test-project');
            expect(response.body).toHaveProperty('job', 'test-job');
            expect(response.body).toHaveProperty('secrets');
            expect(response.body.secrets).toHaveProperty('API_KEY');
        });
    });

    describe('Server initialization', () => {
        it('should create server instance', () => {
            expect(server).toBeDefined();
            expect(server.router).toBeDefined();
        });

        it('should have proper middleware setup', () => {
            expect(server.router).toBeDefined();
        });
    });

    describe('Error handling', () => {
        it('should handle 404 errors', async () => {
            await request(app)
                .get('/api/nonexistent')
                .expect(404);
        });
    });

    describe('CORS handling', () => {
        it('should handle CORS preflight requests', async () => {
            await request(app)
                .options('/api/pipelines')
                .expect(200);
        });
    });

    describe('Content-Type handling', () => {
        it('should handle JSON requests', async () => {
            await request(app)
                .post('/api/pipelines')
                .send({ name: 'test-pipeline' })
                .set('Content-Type', 'application/json')
                .expect(201);
        });
    });

    describe('Authentication', () => {
        it('should handle authenticated requests', async () => {
            await request(app)
                .get('/api/pipelines')
                .set('Authorization', 'Bearer test-token')
                .expect(200);
        });
    });

    describe('GET /api/pipelines/:id', () => {
        it('should return a specific pipeline', async () => {
            const response = await request(app)
                .get('/api/pipelines/pipeline-1')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id', 'pipeline-1');
            expect(response.body).toHaveProperty('name', 'Sample Pipeline');
            expect(response.body).toHaveProperty('status', 'running');
        });

        it('should handle different pipeline IDs', async () => {
            const response = await request(app)
                .get('/api/pipelines/custom-pipeline-123')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id', 'custom-pipeline-123');
            expect(response.body).toHaveProperty('name', 'Sample Pipeline');
            expect(response.body).toHaveProperty('status', 'running');
        });

        it('should handle special characters in pipeline ID', async () => {
            const response = await request(app)
                .get('/api/pipelines/pipeline-with-special-chars-@#$%')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id', 'pipeline-with-special-chars-@#$%');
        });
    });

    describe('POST /api/pipelines', () => {
        it('should create a new pipeline', async () => {
            const pipelineData = {
                name: 'Test Pipeline',
                description: 'A test pipeline',
                steps: ['build', 'test', 'deploy']
            };

            const response = await request(app)
                .post('/api/pipelines')
                .send(pipelineData)
                .expect('Content-Type', /json/)
                .expect(201);

            expect(response.body).toHaveProperty('id', 'new-pipeline');
            expect(response.body).toHaveProperty('name', 'Test Pipeline');
            expect(response.body).toHaveProperty('description', 'A test pipeline');
            expect(response.body).toHaveProperty('steps');
        });

        it('should handle empty pipeline data', async () => {
            const response = await request(app)
                .post('/api/pipelines')
                .send({})
                .expect('Content-Type', /json/)
                .expect(201);

            expect(response.body).toHaveProperty('id', 'new-pipeline');
        });

        it('should handle complex pipeline data', async () => {
            const complexPipelineData = {
                name: 'Complex Pipeline',
                description: 'A complex pipeline with nested configuration',
                steps: [
                    {
                        name: 'build',
                        commands: ['npm install', 'npm run build'],
                        environment: { NODE_ENV: 'production' }
                    },
                    {
                        name: 'test',
                        commands: ['npm test'],
                        environment: { NODE_ENV: 'test' }
                    }
                ],
                triggers: ['push', 'pull_request'],
                schedule: '0 0 * * *'
            };

            const response = await request(app)
                .post('/api/pipelines')
                .send(complexPipelineData)
                .expect('Content-Type', /json/)
                .expect(201);

            expect(response.body).toHaveProperty('id', 'new-pipeline');
            expect(response.body).toHaveProperty('name', 'Complex Pipeline');
            expect(response.body).toHaveProperty('steps');
            expect(response.body).toHaveProperty('triggers');
            expect(response.body).toHaveProperty('schedule');
        });
    });

    describe('GET /api/jobs/:id', () => {
        it('should return a specific job', async () => {
            const response = await request(app)
                .get('/api/jobs/job-1')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id', 'job-1');
            expect(response.body).toHaveProperty('name', 'Sample Job');
            expect(response.body).toHaveProperty('status', 'running');
        });

        it('should handle different job IDs', async () => {
            const response = await request(app)
                .get('/api/jobs/custom-job-456')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id', 'custom-job-456');
            expect(response.body).toHaveProperty('name', 'Sample Job');
            expect(response.body).toHaveProperty('status', 'running');
        });

        it('should handle UUID job IDs', async () => {
            const uuid = '550e8400-e29b-41d4-a716-************';
            const response = await request(app)
                .get(`/api/jobs/${uuid}`)
                .expect('Content-Type', /json/)
                .expect(200);

            expect(response.body).toHaveProperty('id', uuid);
        });
    });

    describe('GET /api/jobs/:id/logs', () => {
        it('should attempt to return job logs', async () => {
            // This will likely fail in test environment since log file doesn't exist
            // but we're testing that the route handler is called
            await request(app)
                .get('/api/jobs/job-1/logs')
                .expect((res) => {
                    // Should either return file or 404/500 error
                    expect([200, 404, 500]).toContain(res.status);
                });
        });

        it('should handle different job IDs for logs', async () => {
            await request(app)
                .get('/api/jobs/test-job-123/logs')
                .expect((res) => {
                    expect([200, 404, 500]).toContain(res.status);
                });
        });

        it('should handle special characters in job ID for logs', async () => {
            await request(app)
                .get('/api/jobs/job-with-special-chars/logs')
                .expect((res) => {
                    expect([200, 404, 500]).toContain(res.status);
                });
        });
    });

    describe('GET /api/jobs/:id/artifacts', () => {
        it('should return job artifacts', async () => {
            const response = await request(app)
                .get('/api/jobs/job-1/artifacts')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body).toContain('artifacts/job-1_output.txt');
        });

        it('should handle different job IDs for artifacts', async () => {
            const response = await request(app)
                .get('/api/jobs/custom-job-789/artifacts')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body).toContain('artifacts/custom-job-789_output.txt');
        });

        it('should return empty array for jobs without artifacts', async () => {
            const response = await request(app)
                .get('/api/jobs/empty-job/artifacts')
                .expect('Content-Type', /json/)
                .expect(200);

            expect(Array.isArray(response.body)).toBe(true);
            expect(response.body).toContain('artifacts/empty-job_output.txt');
        });
    });
});