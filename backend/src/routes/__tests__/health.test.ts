import request from 'supertest';
import express from 'express';
import healthRoutes from '../health';
import { DatabaseService } from '../../services/database';
import { QueueService } from '../../services/queue';
import { MinioService } from '../../services/minio';

// Mock services
jest.mock('../../services/database');
jest.mock('../../services/queue');
jest.mock('../../services/minio');

const mockDatabaseService = DatabaseService as jest.Mocked<typeof DatabaseService>;
const mockQueueService = QueueService as jest.Mocked<typeof QueueService>;
const mockMinioService = MinioService as jest.Mocked<typeof MinioService>;

describe('Health Routes', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use('/health', healthRoutes);
    jest.clearAllMocks();
  });

  describe('GET /health', () => {
    it('should return basic health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect('Content-Type', /json/)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'ChainOps Backend is healthy',
        timestamp: expect.any(String),
        version: expect.any(String),
        environment: expect.any(String),
      });
    });
  });

  describe('GET /health/detailed', () => {
    it('should return detailed health status when all services are healthy', async () => {
      mockDatabaseService.healthCheck.mockResolvedValue(true);
      mockQueueService.healthCheck.mockResolvedValue(true);
      mockMinioService.healthCheck.mockResolvedValue(true);

      const response = await request(app)
        .get('/health/detailed')
        .expect('Content-Type', /json/)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'All services are healthy',
        timestamp: expect.any(String),
        responseTime: expect.any(String),
        checks: {
          database: true,
          queue: true,
          storage: true,
        },
        version: expect.any(String),
        environment: expect.any(String),
      });
    });

    it('should return unhealthy status when database is down', async () => {
      mockDatabaseService.healthCheck.mockRejectedValue(new Error('Database connection failed'));
      mockQueueService.healthCheck.mockResolvedValue(true);
      mockMinioService.healthCheck.mockResolvedValue(true);

      const response = await request(app)
        .get('/health/detailed')
        .expect('Content-Type', /json/)
        .expect(503);

      expect(response.body).toEqual({
        success: false,
        message: 'Some services are unhealthy',
        timestamp: expect.any(String),
        responseTime: expect.any(String),
        checks: {
          database: false,
          queue: true,
          storage: true,
        },
        version: expect.any(String),
        environment: expect.any(String),
      });
    });

    it('should return unhealthy status when queue service is down', async () => {
      mockDatabaseService.healthCheck.mockResolvedValue(true);
      mockQueueService.healthCheck.mockRejectedValue(new Error('Queue connection failed'));
      mockMinioService.healthCheck.mockResolvedValue(true);

      const response = await request(app)
        .get('/health/detailed')
        .expect('Content-Type', /json/)
        .expect(503);

      expect(response.body.checks).toEqual({
        database: true,
        queue: false,
        storage: true,
      });
    });

    it('should return unhealthy status when storage service is down', async () => {
      mockDatabaseService.healthCheck.mockResolvedValue(true);
      mockQueueService.healthCheck.mockResolvedValue(true);
      mockMinioService.healthCheck.mockRejectedValue(new Error('Storage connection failed'));

      const response = await request(app)
        .get('/health/detailed')
        .expect('Content-Type', /json/)
        .expect(503);

      expect(response.body.checks).toEqual({
        database: true,
        queue: true,
        storage: false,
      });
    });
  });

  describe('GET /health/ready', () => {
    it('should return ready status when database is healthy', async () => {
      mockDatabaseService.healthCheck.mockResolvedValue(true);

      const response = await request(app)
        .get('/health/ready')
        .expect('Content-Type', /json/)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Service is ready',
        timestamp: expect.any(String),
      });
    });

    it('should return not ready status when database is unhealthy', async () => {
      mockDatabaseService.healthCheck.mockResolvedValue(false);

      const response = await request(app)
        .get('/health/ready')
        .expect('Content-Type', /json/)
        .expect(503);

      expect(response.body).toEqual({
        success: false,
        message: 'Service is not ready',
        timestamp: expect.any(String),
      });
    });

    it('should handle database health check errors', async () => {
      mockDatabaseService.healthCheck.mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .get('/health/ready')
        .expect('Content-Type', /json/)
        .expect(503);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /health/live', () => {
    it('should return liveness status', async () => {
      const response = await request(app)
        .get('/health/live')
        .expect('Content-Type', /json/)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Service is alive',
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        memory: expect.objectContaining({
          rss: expect.any(Number),
          heapTotal: expect.any(Number),
          heapUsed: expect.any(Number),
          external: expect.any(Number),
          arrayBuffers: expect.any(Number),
        }),
      });
    });

    it('should always return 200 status for liveness probe', async () => {
      // Liveness probe should always return 200 regardless of other service states
      const response = await request(app)
        .get('/health/live')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Service is alive');
    });

    it('should include process uptime and memory usage', async () => {
      const response = await request(app)
        .get('/health/live')
        .expect(200);

      expect(typeof response.body.uptime).toBe('number');
      expect(response.body.uptime).toBeGreaterThanOrEqual(0);
      expect(response.body.memory).toBeDefined();
      expect(typeof response.body.memory.rss).toBe('number');
      expect(typeof response.body.memory.heapTotal).toBe('number');
      expect(typeof response.body.memory.heapUsed).toBe('number');
    });
  });
});
