import request from 'supertest';
import express from 'express';
import healthRoutes from '../health';
import { DatabaseService } from '../../services/database';
import { QueueService } from '../../services/queue';
import { MinioService } from '../../services/minio';

// Mock services
jest.mock('../../services/database');
jest.mock('../../services/queue');
jest.mock('../../services/minio');

const mockDatabaseService = DatabaseService as jest.Mocked<typeof DatabaseService>;
const mockQueueService = QueueService as jest.Mocked<typeof QueueService>;
const mockMinioService = MinioService as jest.Mocked<typeof MinioService>;

describe('Health Routes', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use('/health', healthRoutes);
    jest.clearAllMocks();
  });

  describe('GET /health', () => {
    it('should return basic health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect('Content-Type', /json/)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'ChainOps Backend is healthy',
        timestamp: expect.any(String),
        version: expect.any(String),
        environment: expect.any(String),
      });
    });
  });

  describe('GET /health/detailed', () => {
    it('should return detailed health status when all services are healthy', async () => {
      mockDatabaseService.healthCheck.mockResolvedValue(true);
      mockQueueService.healthCheck.mockResolvedValue(true);
      mockMinioService.healthCheck.mockResolvedValue(true);

      const response = await request(app)
        .get('/health/detailed')
        .expect('Content-Type', /json/)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'All services are healthy',
        timestamp: expect.any(String),
        responseTime: expect.any(String),
        checks: {
          database: true,
          queue: true,
          storage: true,
        },
        version: expect.any(String),
        environment: expect.any(String),
      });
    });

    it('should return unhealthy status when database is down', async () => {
      mockDatabaseService.healthCheck.mockRejectedValue(new Error('Database connection failed'));
      mockQueueService.healthCheck.mockResolvedValue(true);
      mockMinioService.healthCheck.mockResolvedValue(true);

      const response = await request(app)
        .get('/health/detailed')
        .expect('Content-Type', /json/)
        .expect(503);

      expect(response.body).toEqual({
        success: false,
        message: 'Some services are unhealthy',
        timestamp: expect.any(String),
        responseTime: expect.any(String),
        checks: {
          database: false,
          queue: true,
          storage: true,
        },
        version: expect.any(String),
        environment: expect.any(String),
      });
    });

    it('should return unhealthy status when queue service is down', async () => {
      mockDatabaseService.healthCheck.mockResolvedValue(true);
      mockQueueService.healthCheck.mockRejectedValue(new Error('Queue connection failed'));
      mockMinioService.healthCheck.mockResolvedValue(true);

      const response = await request(app)
        .get('/health/detailed')
        .expect('Content-Type', /json/)
        .expect(503);

      expect(response.body.checks).toEqual({
        database: true,
        queue: false,
        storage: true,
      });
    });

    it('should return unhealthy status when storage service is down', async () => {
      mockDatabaseService.healthCheck.mockResolvedValue(true);
      mockQueueService.healthCheck.mockResolvedValue(true);
      mockMinioService.healthCheck.mockRejectedValue(new Error('Storage connection failed'));

      const response = await request(app)
        .get('/health/detailed')
        .expect('Content-Type', /json/)
        .expect(503);

      expect(response.body.checks).toEqual({
        database: true,
        queue: true,
        storage: false,
      });
    });
  });
});
