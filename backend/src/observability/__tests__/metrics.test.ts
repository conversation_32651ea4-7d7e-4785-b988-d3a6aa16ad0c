import { MetricsManager } from '../metrics';
import request from 'supertest';
import express from 'express';

describe('MetricsManager', () => {
    let app: express.Application;

    beforeAll(() => {
        app = express();
        app.get('/metrics', MetricsManager.getMetricsMiddleware());
    });

    it('should record job duration', () => {
        const jobName = 'test-job';
        const status = 'success';
        const duration = 5.5;

        MetricsManager.recordJobDuration(jobName, status, duration);

        // Note: We can't directly test the metric value as it's internal to prom-client
        // Instead, we verify the method doesn't throw
        expect(() => {
            MetricsManager.recordJobDuration(jobName, status, duration);
        }).not.toThrow();
    });

    it('should increment job count', () => {
        const jobName = 'test-job';
        const status = 'success';

        expect(() => {
            MetricsManager.incrementJobCount(jobName, status);
        }).not.toThrow();
    });

    it('should increment pipeline count', () => {
        const pipelineName = 'test-pipeline';
        const status = 'success';

        expect(() => {
            MetricsManager.incrementPipelineCount(pipelineName, status);
        }).not.toThrow();
    });

    it('should record artifact size', () => {
        const jobName = 'test-job';
        const type = 'log';
        const size = 1024;

        expect(() => {
            MetricsManager.recordArtifactSize(jobName, type, size);
        }).not.toThrow();
    });

    it('should expose metrics endpoint', async () => {
        const response = await request(app)
            .get('/metrics')
            .expect('Content-Type', /text\/plain/)
            .expect(200);

        // Verify metrics content
        expect(response.text).toContain('chainops_job_duration_seconds');
        expect(response.text).toContain('chainops_job_total');
        expect(response.text).toContain('chainops_pipeline_total');
        expect(response.text).toContain('chainops_artifact_size_bytes');
    });

    it('should get metrics middleware', () => {
        const middleware = MetricsManager.getMetricsMiddleware();
        expect(middleware).toBeDefined();
        expect(typeof middleware).toBe('function');
    });

    it('should handle multiple job statuses', () => {
        const jobName = 'multi-status-job';

        expect(() => {
            MetricsManager.incrementJobCount(jobName, 'success');
            MetricsManager.incrementJobCount(jobName, 'failure');
            MetricsManager.incrementJobCount(jobName, 'cancelled');
        }).not.toThrow();
    });

    it('should handle multiple pipeline statuses', () => {
        const pipelineName = 'multi-status-pipeline';

        expect(() => {
            MetricsManager.incrementPipelineCount(pipelineName, 'success');
            MetricsManager.incrementPipelineCount(pipelineName, 'failure');
            MetricsManager.incrementPipelineCount(pipelineName, 'running');
        }).not.toThrow();
    });

    it('should handle different artifact types', () => {
        const jobName = 'artifact-job';

        expect(() => {
            MetricsManager.recordArtifactSize(jobName, 'log', 1024);
            MetricsManager.recordArtifactSize(jobName, 'binary', 2048);
            MetricsManager.recordArtifactSize(jobName, 'report', 512);
        }).not.toThrow();
    });
}); 